'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const { userId } = event;
	
	if (!userId) {
		return {
			success: false,
			message: '用户ID不能为空'
		};
	}
	
	try {
		// 获取用户基本信息
		const userResult = await db.collection('user').doc(userId).get();
		
		if (userResult.data.length === 0) {
			return {
				success: false,
				message: '用户不存在'
			};
		}
		
		const user = userResult.data[0];
		
		// 获取用户发布的房源数量
		const publishResult = await db.collection('house')
			.where({ owner_id: userId })
			.count();
		
		// 获取用户收藏数量
		let collectCount = 0;
		try {
			const collectResult = await db.collection('user_collect')
				.where({ user_id: userId })
				.count();
			collectCount = collectResult.total;
		} catch (error) {
			// 收藏表可能不存在
		}
		
		// 获取用户最近发布的房源
		const recentHousesResult = await db.collection('house')
			.where({ owner_id: userId })
			.orderBy('created_at', 'desc')
			.limit(5)
			.get();
		
		// 获取用户访问记录数量
		let visitCount = 0;
		try {
			const visitResult = await db.collection('visit_logs')
				.where({ user_id: userId })
				.count();
			visitCount = visitResult.total;
		} catch (error) {
			// 访问日志表可能不存在
		}
		
		// 组装用户详情
		const userDetail = {
			...user,
			publishCount: publishResult.total,
			collectCount: collectCount,
			visitCount: visitCount,
			recentHouses: recentHousesResult.data,
			status: user.status || 'active'
		};
		
		return {
			success: true,
			data: userDetail
		};
		
	} catch (error) {
		console.error('获取用户详情失败:', error);
		return {
			success: false,
			message: '获取用户详情失败',
			error: error.message
		};
	}
};
