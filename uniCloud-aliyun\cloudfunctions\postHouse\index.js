'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const {
		title,
		images,
		price,
		type,
		location,
		config,
		contact,
		desc
	} = event;
	
	// 表单验证
	if (!title || !title.trim()) {
		return {
			success: false,
			message: '房源标题不能为空'
		};
	}
	
	if (!images || images.length === 0) {
		return {
			success: false,
			message: '请上传房源图片'
		};
	}
	
	if (!price || price <= 0) {
		return {
			success: false,
			message: '请输入正确的租金'
		};
	}
	
	if (!type) {
		return {
			success: false,
			message: '请选择户型'
		};
	}
	
	if (!location || !location.address) {
		return {
			success: false,
			message: '请选择位置'
		};
	}
	
	if (!contact || (!contact.phone && !contact.wechat)) {
		return {
			success: false,
			message: '请至少填写一种联系方式'
		};
	}
	
	try {
		// 构建房源数据
		const houseData = {
			title: title.trim(),
			desc: desc ? desc.trim() : '',
			images: images,
			location: {
				address: location.address,
				latitude: location.latitude || '',
				longitude: location.longitude || ''
			},
			price: Number(price),
			type: type,
			config: config || [],
			contact: {
				phone: contact.phone || '',
				wechat: contact.wechat || ''
			},
			owner_id: context.CLIENTIP, // 临时用IP作为用户标识
			status: 'pending', // 待审核状态
			view_count: 0,
			created_at: new Date(),
			updated_at: new Date()
		};
		
		// 插入房源数据
		const result = await db.collection('house').add(houseData);
		
		// 记录发布日志
		const publishLog = {
			house_id: result.id,
			house_title: title,
			publisher_ip: context.CLIENTIP,
			publish_time: new Date(),
			status: 'pending'
		};
		
		await db.collection('publish_logs').add(publishLog);
		
		return {
			success: true,
			message: '房源发布成功，等待审核',
			data: {
				houseId: result.id,
				status: 'pending'
			}
		};
		
	} catch (error) {
		console.error('发布房源失败:', error);
		return {
			success: false,
			message: '发布失败',
			error: error.message
		};
	}
};
