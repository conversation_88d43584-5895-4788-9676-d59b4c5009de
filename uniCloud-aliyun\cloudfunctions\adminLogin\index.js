'use strict';

const db = uniCloud.database();
const crypto = require('crypto');

exports.main = async (event, context) => {
	const { username, password } = event;
	
	if (!username || !password) {
		return {
			success: false,
			message: '用户名和密码不能为空'
		};
	}
	
	try {
		// 查询管理员账号
		const adminResult = await db.collection('admin')
			.where({
				username: username
			})
			.get();
		
		// 如果没有找到管理员，创建默认管理员
		if (adminResult.data.length === 0) {
			// 创建默认管理员账号
			const defaultAdmin = {
				username: 'admin',
				password: generatePasswordHash('123456'), // 默认密码
				role: 'super_admin',
				status: 'active',
				created_at: new Date(),
				last_login: null
			};
			
			await db.collection('admin').add(defaultAdmin);
			
			// 如果输入的是默认账号，继续验证
			if (username === 'admin') {
				if (password === '123456') {
					// 生成token
					const token = generateToken(username);
					
					// 更新最后登录时间
					await db.collection('admin')
						.where({ username: username })
						.update({
							last_login: new Date()
						});
					
					return {
						success: true,
						message: '登录成功',
						token: token,
						adminInfo: {
							username: username,
							role: 'super_admin'
						}
					};
				}
			}
		} else {
			// 验证密码
			const admin = adminResult.data[0];
			
			if (admin.status !== 'active') {
				return {
					success: false,
					message: '账号已被禁用'
				};
			}
			
			const inputPasswordHash = generatePasswordHash(password);
			if (admin.password === inputPasswordHash) {
				// 生成token
				const token = generateToken(admin.username);
				
				// 更新最后登录时间
				await db.collection('admin')
					.doc(admin._id)
					.update({
						last_login: new Date()
					});
				
				return {
					success: true,
					message: '登录成功',
					token: token,
					adminInfo: {
						username: admin.username,
						role: admin.role
					}
				};
			}
		}
		
		return {
			success: false,
			message: '用户名或密码错误'
		};
		
	} catch (error) {
		console.error('管理员登录失败:', error);
		return {
			success: false,
			message: '登录失败',
			error: error.message
		};
	}
};

// 生成密码哈希
function generatePasswordHash(password) {
	return crypto.createHash('md5').update(password + 'house_rental_salt').digest('hex');
}

// 生成token
function generateToken(username) {
	const timestamp = Date.now();
	const randomStr = Math.random().toString(36).substr(2, 9);
	const tokenData = `${username}_${timestamp}_${randomStr}`;
	return crypto.createHash('md5').update(tokenData).digest('hex');
}
