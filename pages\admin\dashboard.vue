<template>
	<view class="admin-dashboard">
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left">
				<text class="page-title">数据统计</text>
			</view>
			<view class="nav-right">
				<text class="admin-name">{{ adminInfo.username }}</text>
				<text class="logout-btn" @click="handleLogout">退出</text>
			</view>
		</view>
		
		<!-- 统计卡片 -->
		<view class="stats-cards">
			<view class="stat-card">
				<view class="card-icon today">📊</view>
				<view class="card-content">
					<text class="card-number">{{ statistics.todayPublish }}</text>
					<text class="card-label">今日发布</text>
				</view>
			</view>
			
			<view class="stat-card">
				<view class="card-icon visit">👁️</view>
				<view class="card-content">
					<text class="card-number">{{ statistics.todayVisit }}</text>
					<text class="card-label">今日访问</text>
				</view>
			</view>
			
			<view class="stat-card">
				<view class="card-icon total">🏠</view>
				<view class="card-content">
					<text class="card-number">{{ statistics.totalHouse }}</text>
					<text class="card-label">房源总数</text>
				</view>
			</view>
			
			<view class="stat-card">
				<view class="card-icon pending">⏳</view>
				<view class="card-content">
					<text class="card-number">{{ statistics.pendingHouse }}</text>
					<text class="card-label">待审核</text>
				</view>
			</view>
		</view>
		
		<!-- 快捷操作 -->
		<view class="quick-actions">
			<view class="section-title">快捷操作</view>
			<view class="action-grid">
				<view class="action-item" @click="goToHouseManage">
					<view class="action-icon">🏘️</view>
					<text class="action-text">房源管理</text>
				</view>
				<view class="action-item" @click="goToUserManage">
					<view class="action-icon">👥</view>
					<text class="action-text">用户管理</text>
				</view>
				<view class="action-item" @click="goToSettings">
					<view class="action-icon">⚙️</view>
					<text class="action-text">系统设置</text>
				</view>
				<view class="action-item" @click="refreshData">
					<view class="action-icon">🔄</view>
					<text class="action-text">刷新数据</text>
				</view>
			</view>
		</view>
		
		<!-- 最新房源 -->
		<view class="recent-houses">
			<view class="section-title">最新房源</view>
			<view class="house-list">
				<view class="house-item" v-for="(house, index) in recentHouses" :key="house._id">
					<image :src="house.images[0]" mode="aspectFill" class="house-image"></image>
					<view class="house-info">
						<text class="house-title">{{ house.title }}</text>
						<text class="house-price">¥{{ house.price }}/月</text>
						<text class="house-status" :class="house.status">{{ getStatusText(house.status) }}</text>
					</view>
					<view class="house-actions">
						<text class="action-btn" @click="auditHouse(house._id, 'approved')" v-if="house.status === 'pending'">通过</text>
						<text class="action-btn reject" @click="auditHouse(house._id, 'rejected')" v-if="house.status === 'pending'">驳回</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			adminInfo: {},
			statistics: {
				todayPublish: 0,
				todayVisit: 0,
				totalHouse: 0,
				pendingHouse: 0
			},
			recentHouses: []
		}
	},
	onLoad() {
		this.checkAuth();
		this.loadData();
	},
	onPullDownRefresh() {
		this.loadData();
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	methods: {
		// 检查权限
		checkAuth() {
			const adminToken = uni.getStorageSync('adminToken');
			const adminInfo = uni.getStorageSync('adminInfo');
			
			if (!adminToken) {
				uni.redirectTo({
					url: '/pages/admin/login'
				});
				return;
			}
			
			this.adminInfo = adminInfo || {};
		},
		
		// 加载数据
		async loadData() {
			await Promise.all([
				this.loadStatistics(),
				this.loadRecentHouses()
			]);
		},
		
		// 加载统计数据
		async loadStatistics() {
			try {
				const res = await uniCloud.callFunction({
					name: 'getStatistics',
					data: {}
				});
				
				if (res.result.success) {
					this.statistics = res.result.data;
				}
			} catch (error) {
				console.error('加载统计数据失败:', error);
			}
		},
		
		// 加载最新房源
		async loadRecentHouses() {
			try {
				const res = await uniCloud.callFunction({
					name: 'getHouseList',
					data: {
						page: 1,
						pageSize: 5,
						status: 'pending'
					}
				});
				
				if (res.result.success) {
					this.recentHouses = res.result.data;
				}
			} catch (error) {
				console.error('加载最新房源失败:', error);
			}
		},
		
		// 审核房源
		async auditHouse(houseId, status) {
			try {
				const res = await uniCloud.callFunction({
					name: 'auditHouse',
					data: {
						houseId: houseId,
						status: status,
						reason: status === 'rejected' ? '不符合发布要求' : ''
					}
				});
				
				if (res.result.success) {
					uni.showToast({
						title: status === 'approved' ? '审核通过' : '已驳回',
						icon: 'success'
					});
					this.loadRecentHouses();
					this.loadStatistics();
				}
			} catch (error) {
				console.error('审核失败:', error);
				uni.showToast({
					title: '操作失败',
					icon: 'none'
				});
			}
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'pending': '待审核',
				'approved': '已通过',
				'rejected': '已驳回'
			};
			return statusMap[status] || '未知';
		},
		
		// 跳转到房源管理
		goToHouseManage() {
			uni.navigateTo({
				url: '/pages/admin/houseManage'
			});
		},
		
		// 跳转到用户管理
		goToUserManage() {
			uni.navigateTo({
				url: '/pages/admin/userManage'
			});
		},
		
		// 跳转到系统设置
		goToSettings() {
			uni.navigateTo({
				url: '/pages/admin/settings'
			});
		},
		
		// 刷新数据
		refreshData() {
			uni.showLoading({ title: '刷新中...' });
			this.loadData().finally(() => {
				uni.hideLoading();
				uni.showToast({
					title: '刷新完成',
					icon: 'success'
				});
			});
		},
		
		// 退出登录
		handleLogout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出管理后台吗？',
				success: (res) => {
					if (res.confirm) {
						uni.removeStorageSync('adminToken');
						uni.removeStorageSync('adminInfo');
						uni.redirectTo({
							url: '/pages/admin/login'
						});
					}
				}
			});
		}
	}
}
</script>

<style scoped>
.admin-dashboard {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 顶部导航 */
.top-nav {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	background-color: #fff;
	border-bottom: 1rpx solid #eee;
}

.page-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.nav-right {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.admin-name {
	font-size: 28rpx;
	color: #666;
}

.logout-btn {
	padding: 10rpx 20rpx;
	background-color: #ff4757;
	color: #fff;
	border-radius: 20rpx;
	font-size: 24rpx;
}

/* 统计卡片 */
.stats-cards {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	padding: 30rpx;
}

.stat-card {
	flex: 1;
	min-width: 300rpx;
	display: flex;
	align-items: center;
	padding: 30rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 36rpx;
	margin-right: 20rpx;
}

.card-icon.today {
	background-color: #e8f4fd;
}

.card-icon.visit {
	background-color: #fff7e6;
}

.card-icon.total {
	background-color: #f6ffed;
}

.card-icon.pending {
	background-color: #fff2f0;
}

.card-content {
	display: flex;
	flex-direction: column;
}

.card-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.card-label {
	font-size: 24rpx;
	color: #666;
}

/* 快捷操作 */
.quick-actions {
	margin: 20rpx 30rpx;
	padding: 30rpx;
	background-color: #fff;
	border-radius: 16rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.action-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 30rpx;
}

.action-item {
	flex: 1;
	min-width: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
}

.action-icon {
	font-size: 48rpx;
	margin-bottom: 15rpx;
}

.action-text {
	font-size: 26rpx;
	color: #666;
}

/* 最新房源 */
.recent-houses {
	margin: 20rpx 30rpx;
	padding: 30rpx;
	background-color: #fff;
	border-radius: 16rpx;
}

.house-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.house-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
}

.house-image {
	width: 120rpx;
	height: 90rpx;
	border-radius: 8rpx;
	margin-right: 20rpx;
}

.house-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.house-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

.house-price {
	font-size: 26rpx;
	color: #ff6b35;
}

.house-status {
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	align-self: flex-start;
}

.house-status.pending {
	background-color: #fff7e6;
	color: #fa8c16;
}

.house-actions {
	display: flex;
	gap: 15rpx;
}

.action-btn {
	padding: 10rpx 20rpx;
	background-color: #52c41a;
	color: #fff;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.action-btn.reject {
	background-color: #ff4757;
}
</style>
