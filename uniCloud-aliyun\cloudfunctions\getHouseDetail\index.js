'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const { houseId } = event;
	
	if (!houseId) {
		return {
			success: false,
			message: '房源ID不能为空'
		};
	}
	
	try {
		// 获取房源详情
		const houseResult = await db.collection('house').doc(houseId).get();
		
		if (houseResult.data.length === 0) {
			return {
				success: false,
				message: '房源不存在'
			};
		}
		
		const houseInfo = houseResult.data[0];
		
		// 检查房源状态
		if (houseInfo.status !== 'approved') {
			return {
				success: false,
				message: '房源暂不可查看'
			};
		}
		
		// 获取用户收藏状态（如果有用户登录）
		let isCollected = false;
		if (context.CLIENTIP) {
			// 这里可以根据用户ID查询收藏状态
			// const collectResult = await db.collection('user_collect')
			//   .where({
			//     user_id: context.CLIENTIP, // 临时用IP代替用户ID
			//     house_id: houseId
			//   }).get();
			// isCollected = collectResult.data.length > 0;
		}
		
		// 增加浏览次数
		await db.collection('house').doc(houseId).update({
			view_count: db.command.inc(1)
		});
		
		// 返回房源详情
		const result = {
			...houseInfo,
			isCollected: isCollected,
			images: houseInfo.images || ['/static/default-house.jpg']
		};
		
		return {
			success: true,
			data: result
		};
		
	} catch (error) {
		console.error('获取房源详情失败:', error);
		return {
			success: false,
			message: '获取房源详情失败',
			error: error.message
		};
	}
};
