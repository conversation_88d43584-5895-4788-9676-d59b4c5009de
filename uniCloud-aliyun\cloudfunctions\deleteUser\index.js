'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const { userId } = event;
	
	if (!userId) {
		return {
			success: false,
			message: '用户ID不能为空'
		};
	}
	
	try {
		// 检查用户是否存在
		const userResult = await db.collection('user').doc(userId).get();
		
		if (userResult.data.length === 0) {
			return {
				success: false,
				message: '用户不存在'
			};
		}
		
		const user = userResult.data[0];
		
		// 删除用户发布的所有房源
		const userHousesResult = await db.collection('house')
			.where({ owner_id: userId })
			.get();
		
		for (const house of userHousesResult.data) {
			// 删除房源
			await db.collection('house').doc(house._id).remove();
			
			// 删除相关的收藏记录
			try {
				await db.collection('user_collect')
					.where({ house_id: house._id })
					.remove();
			} catch (error) {
				console.log('删除收藏记录失败:', error);
			}
			
			// 删除相关的访问记录
			try {
				await db.collection('visit_logs')
					.where({ house_id: house._id })
					.remove();
			} catch (error) {
				console.log('删除访问记录失败:', error);
			}
		}
		
		// 删除用户的收藏记录
		try {
			await db.collection('user_collect')
				.where({ user_id: userId })
				.remove();
		} catch (error) {
			console.log('删除用户收藏记录失败:', error);
		}
		
		// 删除用户的访问记录
		try {
			await db.collection('visit_logs')
				.where({ user_id: userId })
				.remove();
		} catch (error) {
			console.log('删除用户访问记录失败:', error);
		}
		
		// 删除用户
		await db.collection('user').doc(userId).remove();
		
		// 记录删除日志
		const deleteLog = {
			user_id: userId,
			user_nickname: user.nickname || '微信用户',
			user_phone: user.phone || '',
			delete_time: new Date(),
			admin_ip: context.CLIENTIP,
			houses_deleted: userHousesResult.data.length
		};
		
		await db.collection('user_delete_logs').add(deleteLog);
		
		return {
			success: true,
			message: '用户删除成功',
			data: {
				deletedHouses: userHousesResult.data.length
			}
		};
		
	} catch (error) {
		console.error('删除用户失败:', error);
		return {
			success: false,
			message: '删除用户失败',
			error: error.message
		};
	}
};
