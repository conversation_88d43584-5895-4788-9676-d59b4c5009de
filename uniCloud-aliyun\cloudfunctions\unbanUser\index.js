'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const { userId } = event;
	
	if (!userId) {
		return {
			success: false,
			message: '用户ID不能为空'
		};
	}
	
	try {
		// 检查用户是否存在
		const userResult = await db.collection('user').doc(userId).get();
		
		if (userResult.data.length === 0) {
			return {
				success: false,
				message: '用户不存在'
			};
		}
		
		const user = userResult.data[0];
		
		// 检查用户是否被封禁
		if (user.status !== 'banned') {
			return {
				success: false,
				message: '用户未被封禁'
			};
		}
		
		// 更新用户状态
		await db.collection('user').doc(userId).update({
			status: 'active',
			unban_time: new Date(),
			ban_reason: db.command.remove(),
			ban_time: db.command.remove()
		});
		
		// 恢复用户发布的房源状态（需要重新审核）
		await db.collection('house')
			.where({ 
				owner_id: userId,
				status: 'banned'
			})
			.update({
				status: 'pending', // 恢复为待审核状态
				unban_time: new Date(),
				ban_time: db.command.remove()
			});
		
		// 记录解封日志
		const unbanLog = {
			user_id: userId,
			user_nickname: user.nickname || '微信用户',
			action: 'unban',
			unban_time: new Date(),
			admin_ip: context.CLIENTIP
		};
		
		await db.collection('user_ban_logs').add(unbanLog);
		
		return {
			success: true,
			message: '用户解封成功'
		};
		
	} catch (error) {
		console.error('解封用户失败:', error);
		return {
			success: false,
			message: '解封用户失败',
			error: error.message
		};
	}
};
