<template>
	<view class="post-house">
		<form @submit="submitForm">
			<!-- 房源标题 -->
			<view class="form-section">
				<view class="section-title">房源标题</view>
				<input v-model="formData.title" placeholder="请输入房源标题" class="form-input" maxlength="50" />
			</view>
			
			<!-- 房源图片 -->
			<view class="form-section">
				<view class="section-title">房源图片 (最多9张)</view>
				<view class="image-upload">
					<view class="image-item" v-for="(image, index) in formData.images" :key="index">
						<image :src="image" mode="aspectFill" class="uploaded-image"></image>
						<text class="delete-btn" @click="deleteImage(index)">×</text>
					</view>
					<view class="upload-btn" v-if="formData.images.length < 9" @click="chooseImage">
						<text class="upload-icon">+</text>
						<text class="upload-text">添加图片</text>
					</view>
				</view>
			</view>
			
			<!-- 基本信息 -->
			<view class="form-section">
				<view class="section-title">基本信息</view>
				<view class="form-row">
					<text class="form-label">租金</text>
					<input v-model="formData.price" type="number" placeholder="请输入月租金" class="form-input" />
					<text class="form-unit">元/月</text>
				</view>
				<view class="form-row">
					<text class="form-label">户型</text>
					<picker :value="typeIndex" :range="typeList" @change="onTypeChange" class="form-picker">
						<view class="picker-text">{{ typeList[typeIndex] || '请选择户型' }}</view>
					</picker>
				</view>
			</view>
			
			<!-- 位置信息 -->
			<view class="form-section">
				<view class="section-title">位置信息</view>
				<view class="location-input" @click="chooseLocation">
					<text class="location-text">{{ formData.location.address || '点击选择位置' }}</text>
					<text class="location-icon">📍</text>
				</view>
			</view>
			
			<!-- 房源配置 -->
			<view class="form-section">
				<view class="section-title">房源配置</view>
				<view class="config-grid">
					<view class="config-item" 
						  v-for="(config, index) in configList" 
						  :key="index"
						  :class="{ active: formData.config.includes(config) }"
						  @click="toggleConfig(config)">
						<text class="config-icon">{{ getConfigIcon(config) }}</text>
						<text class="config-text">{{ config }}</text>
					</view>
				</view>
			</view>
			
			<!-- 联系方式 -->
			<view class="form-section">
				<view class="section-title">联系方式</view>
				<view class="form-row">
					<text class="form-label">电话</text>
					<input v-model="formData.contact.phone" placeholder="请输入联系电话" class="form-input" />
				</view>
				<view class="form-row">
					<text class="form-label">微信</text>
					<input v-model="formData.contact.wechat" placeholder="请输入微信号" class="form-input" />
				</view>
			</view>
			
			<!-- 详细描述 -->
			<view class="form-section">
				<view class="section-title">详细描述</view>
				<textarea v-model="formData.desc" 
						  placeholder="请详细描述房源情况，如周边环境、交通便利性等" 
						  class="form-textarea"
						  maxlength="500"></textarea>
			</view>
			
			<!-- 提交按钮 -->
			<view class="submit-section">
				<button class="submit-btn" @click="submitForm" :disabled="submitting">
					{{ submitting ? '发布中...' : '发布房源' }}
				</button>
			</view>
		</form>
	</view>
</template>

<script>
export default {
	data() {
		return {
			formData: {
				title: '',
				images: [],
				price: '',
				type: '',
				location: {
					address: '',
					latitude: '',
					longitude: ''
				},
				config: [],
				contact: {
					phone: '',
					wechat: ''
				},
				desc: ''
			},
			typeList: ['一居室', '二居室', '三居室', '合租', '整租'],
			typeIndex: -1,
			configList: ['空调', '洗衣机', '热水器', '冰箱', '电视', 'WiFi', '床', '衣柜', '桌椅', '阳台'],
			submitting: false
		}
	},
	methods: {
		// 选择图片
		chooseImage() {
			const remainCount = 9 - this.formData.images.length;
			uni.chooseImage({
				count: remainCount,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.uploadImages(res.tempFilePaths);
				}
			});
		},
		
		// 上传图片
		async uploadImages(tempFilePaths) {
			uni.showLoading({ title: '上传中...' });
			
			try {
				for (let filePath of tempFilePaths) {
					const result = await uniCloud.uploadFile({
						filePath: filePath,
						cloudPath: `house-images/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.jpg`
					});
					this.formData.images.push(result.fileID);
				}
			} catch (error) {
				console.error('图片上传失败:', error);
				uni.showToast({
					title: '图片上传失败',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},
		
		// 删除图片
		deleteImage(index) {
			this.formData.images.splice(index, 1);
		},
		
		// 选择户型
		onTypeChange(e) {
			this.typeIndex = e.detail.value;
			this.formData.type = this.typeList[this.typeIndex];
		},
		
		// 选择位置
		chooseLocation() {
			uni.chooseLocation({
				success: (res) => {
					this.formData.location = {
						address: res.address,
						latitude: res.latitude,
						longitude: res.longitude
					};
				},
				fail: (error) => {
					console.error('选择位置失败:', error);
					uni.showToast({
						title: '请授权位置信息',
						icon: 'none'
					});
				}
			});
		},
		
		// 切换配置
		toggleConfig(config) {
			const index = this.formData.config.indexOf(config);
			if (index > -1) {
				this.formData.config.splice(index, 1);
			} else {
				this.formData.config.push(config);
			}
		},
		
		// 获取配置图标
		getConfigIcon(config) {
			const iconMap = {
				'空调': '❄️',
				'洗衣机': '🧺',
				'热水器': '🚿',
				'冰箱': '🧊',
				'电视': '📺',
				'WiFi': '📶',
				'床': '🛏️',
				'衣柜': '👔',
				'桌椅': '🪑',
				'阳台': '🌅'
			};
			return iconMap[config] || '✅';
		},
		
		// 表单验证
		validateForm() {
			if (!this.formData.title.trim()) {
				uni.showToast({ title: '请输入房源标题', icon: 'none' });
				return false;
			}
			
			if (this.formData.images.length === 0) {
				uni.showToast({ title: '请上传房源图片', icon: 'none' });
				return false;
			}
			
			if (!this.formData.price || this.formData.price <= 0) {
				uni.showToast({ title: '请输入正确的租金', icon: 'none' });
				return false;
			}
			
			if (!this.formData.type) {
				uni.showToast({ title: '请选择户型', icon: 'none' });
				return false;
			}
			
			if (!this.formData.location.address) {
				uni.showToast({ title: '请选择位置', icon: 'none' });
				return false;
			}
			
			if (!this.formData.contact.phone && !this.formData.contact.wechat) {
				uni.showToast({ title: '请至少填写一种联系方式', icon: 'none' });
				return false;
			}
			
			return true;
		},
		
		// 提交表单
		async submitForm() {
			if (!this.validateForm()) return;
			
			this.submitting = true;
			
			try {
				const res = await uniCloud.callFunction({
					name: 'postHouse',
					data: this.formData
				});
				
				if (res.result.success) {
					uni.showToast({
						title: '发布成功，等待审核',
						icon: 'success'
					});
					
					// 清空表单
					this.resetForm();
					
					// 跳转到我的发布页面
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/myPublish/myPublish'
						});
					}, 1500);
				} else {
					uni.showToast({
						title: res.result.message || '发布失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('发布房源失败:', error);
				uni.showToast({
					title: '发布失败',
					icon: 'none'
				});
			} finally {
				this.submitting = false;
			}
		},
		
		// 重置表单
		resetForm() {
			this.formData = {
				title: '',
				images: [],
				price: '',
				type: '',
				location: {
					address: '',
					latitude: '',
					longitude: ''
				},
				config: [],
				contact: {
					phone: '',
					wechat: ''
				},
				desc: ''
			};
			this.typeIndex = -1;
		}
	}
}
</script>

<style scoped>
.post-house {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

/* 表单区域样式 */
.form-section {
	background-color: #fff;
	margin-bottom: 20rpx;
	padding: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.form-input {
	width: 100%;
	padding: 25rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
	font-size: 28rpx;
	margin-bottom: 20rpx;
}

.form-row {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.form-label {
	width: 120rpx;
	font-size: 28rpx;
	color: #333;
}

.form-unit {
	margin-left: 20rpx;
	font-size: 28rpx;
	color: #666;
}

.form-picker {
	flex: 1;
	padding: 25rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
}

.picker-text {
	font-size: 28rpx;
	color: #333;
}

.form-textarea {
	width: 100%;
	min-height: 200rpx;
	padding: 25rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
	font-size: 28rpx;
}

/* 图片上传样式 */
.image-upload {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.image-item {
	position: relative;
	width: 200rpx;
	height: 200rpx;
}

.uploaded-image {
	width: 100%;
	height: 100%;
	border-radius: 12rpx;
}

.delete-btn {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	background-color: #ff4757;
	color: #fff;
	border-radius: 50%;
	text-align: center;
	line-height: 40rpx;
	font-size: 24rpx;
}

.upload-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 200rpx;
	height: 200rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
	border: 2rpx dashed #ccc;
}

.upload-icon {
	font-size: 48rpx;
	color: #999;
	margin-bottom: 10rpx;
}

.upload-text {
	font-size: 24rpx;
	color: #999;
}

/* 位置选择样式 */
.location-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 25rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
}

.location-text {
	font-size: 28rpx;
	color: #333;
}

.location-icon {
	font-size: 32rpx;
}

/* 配置选择样式 */
.config-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.config-item {
	display: flex;
	align-items: center;
	padding: 20rpx 25rpx;
	background-color: #f5f5f5;
	border-radius: 30rpx;
	border: 2rpx solid transparent;
}

.config-item.active {
	background-color: #e8f4fd;
	border-color: #1890ff;
}

.config-icon {
	font-size: 28rpx;
	margin-right: 10rpx;
}

.config-text {
	font-size: 26rpx;
	color: #333;
}

/* 提交按钮样式 */
.submit-section {
	padding: 40rpx;
}

.submit-btn {
	width: 100%;
	padding: 30rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 12rpx;
	font-size: 32rpx;
	text-align: center;
}

.submit-btn[disabled] {
	background-color: #ccc;
}
</style>
