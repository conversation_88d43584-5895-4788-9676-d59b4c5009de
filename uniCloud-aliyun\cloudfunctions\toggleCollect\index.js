'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const { houseId } = event;
	
	if (!houseId) {
		return {
			success: false,
			message: '房源ID不能为空'
		};
	}
	
	try {
		const userId = context.CLIENTIP; // 临时用IP作为用户标识
		
		// 检查是否已收藏
		const collectResult = await db.collection('user_collect')
			.where({
				user_id: userId,
				house_id: houseId
			})
			.get();
		
		if (collectResult.data.length > 0) {
			// 已收藏，取消收藏
			await db.collection('user_collect')
				.doc(collectResult.data[0]._id)
				.remove();
			
			return {
				success: true,
				message: '取消收藏成功',
				isCollected: false
			};
		} else {
			// 未收藏，添加收藏
			await db.collection('user_collect').add({
				user_id: userId,
				house_id: houseId,
				collect_time: new Date()
			});
			
			return {
				success: true,
				message: '收藏成功',
				isCollected: true
			};
		}
		
	} catch (error) {
		console.error('收藏操作失败:', error);
		return {
			success: false,
			message: '收藏操作失败',
			error: error.message
		};
	}
};
