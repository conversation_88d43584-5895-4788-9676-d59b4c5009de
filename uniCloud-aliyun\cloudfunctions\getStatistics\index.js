'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	try {
		// 获取今天的开始和结束时间
		const today = new Date();
		const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
		const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);
		
		// 今日发布房源数
		const todayPublishResult = await db.collection('house')
			.where({
				created_at: db.command.gte(todayStart).and(db.command.lt(todayEnd)),
				status: db.command.neq('rejected')
			})
			.count();
		
		// 总房源数（已审核通过的）
		const totalHouseResult = await db.collection('house')
			.where({
				status: 'approved'
			})
			.count();
		
		// 今日访问量（从访问日志表获取）
		let todayVisit = 0;
		try {
			const visitResult = await db.collection('visit_logs')
				.where({
					visit_time: db.command.gte(todayStart).and(db.command.lt(todayEnd))
				})
				.count();
			todayVisit = visitResult.total;
		} catch (error) {
			// 如果访问日志表不存在，返回0
			console.log('访问日志表不存在，返回默认值');
		}
		
		// 用户总数
		let totalUsers = 0;
		try {
			const userResult = await db.collection('user').count();
			totalUsers = userResult.total;
		} catch (error) {
			// 如果用户表不存在，返回0
			console.log('用户表不存在，返回默认值');
		}
		
		// 待审核房源数
		const pendingHouseResult = await db.collection('house')
			.where({
				status: 'pending'
			})
			.count();
		
		// 本周发布趋势
		const weekStart = new Date(todayStart.getTime() - 6 * 24 * 60 * 60 * 1000);
		const weekTrendResult = await db.collection('house')
			.where({
				created_at: db.command.gte(weekStart).and(db.command.lt(todayEnd))
			})
			.field({
				created_at: true
			})
			.get();
		
		// 按天统计本周发布数量
		const weekTrend = [];
		for (let i = 6; i >= 0; i--) {
			const dayStart = new Date(todayStart.getTime() - i * 24 * 60 * 60 * 1000);
			const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);
			const dayCount = weekTrendResult.data.filter(item => {
				const itemDate = new Date(item.created_at);
				return itemDate >= dayStart && itemDate < dayEnd;
			}).length;
			
			weekTrend.push({
				date: `${dayStart.getMonth() + 1}-${dayStart.getDate()}`,
				count: dayCount
			});
		}
		
		return {
			success: true,
			data: {
				todayPublish: todayPublishResult.total,
				todayVisit: todayVisit,
				totalHouse: totalHouseResult.total,
				totalUsers: totalUsers,
				pendingHouse: pendingHouseResult.total,
				weekTrend: weekTrend
			}
		};
		
	} catch (error) {
		console.error('获取统计数据失败:', error);
		return {
			success: false,
			message: '获取统计数据失败',
			error: error.message
		};
	}
};
