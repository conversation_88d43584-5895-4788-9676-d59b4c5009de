<template>
	<view class="my-page">
		<!-- 用户信息区域 -->
		<view class="user-section">
			<view class="user-avatar">
				<image :src="userInfo.avatar || '/static/default-avatar.png'" mode="aspectFill" class="avatar-image" @click="editProfile"></image>
			</view>
			<view class="user-info">
				<text class="user-name">{{ userInfo.nickname || '微信用户' }}</text>
				<text class="user-phone">{{ userInfo.phone || '未绑定手机' }}</text>
			</view>
			<view class="user-actions">
				<text class="edit-btn" @click="editProfile">编辑</text>
			</view>
		</view>
		
		<!-- 统计信息 -->
		<view class="stats-section">
			<view class="stat-item" @click="goToMyPublish">
				<text class="stat-number">{{ userStats.publishCount }}</text>
				<text class="stat-label">我的发布</text>
			</view>
			<view class="stat-item" @click="goToMyCollect">
				<text class="stat-number">{{ userStats.collectCount }}</text>
				<text class="stat-label">我的收藏</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ userStats.viewCount }}</text>
				<text class="stat-label">浏览记录</text>
			</view>
		</view>
		
		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-item" @click="goToMyPublish">
				<view class="menu-icon">📤</view>
				<text class="menu-text">我的发布</text>
				<text class="menu-arrow">></text>
			</view>
			<view class="menu-item" @click="goToMyCollect">
				<view class="menu-icon">❤️</view>
				<text class="menu-text">我的收藏</text>
				<text class="menu-arrow">></text>
			</view>
			<view class="menu-item" @click="goToViewHistory">
				<view class="menu-icon">👁️</view>
				<text class="menu-text">浏览记录</text>
				<text class="menu-arrow">></text>
			</view>
		</view>
		
		<!-- 设置菜单 -->
		<view class="menu-section">
			<view class="menu-item" @click="goToSettings">
				<view class="menu-icon">⚙️</view>
				<text class="menu-text">设置</text>
				<text class="menu-arrow">></text>
			</view>
			<view class="menu-item" @click="goToHelp">
				<view class="menu-icon">❓</view>
				<text class="menu-text">帮助与反馈</text>
				<text class="menu-arrow">></text>
			</view>
			<view class="menu-item" @click="goToAbout">
				<view class="menu-icon">ℹ️</view>
				<text class="menu-text">关于我们</text>
				<text class="menu-arrow">></text>
			</view>
		</view>
		
		<!-- 管理员入口 -->
		<view class="admin-section" v-if="showAdminEntry">
			<view class="menu-item admin-entry" @click="goToAdmin">
				<view class="menu-icon">👨‍💼</view>
				<text class="menu-text">管理后台</text>
				<text class="menu-arrow">></text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: {
				nickname: '',
				avatar: '',
				phone: ''
			},
			userStats: {
				publishCount: 0,
				collectCount: 0,
				viewCount: 0
			},
			showAdminEntry: false
		}
	},
	onLoad() {
		this.loadUserInfo();
		this.loadUserStats();
		this.checkAdminAccess();
	},
	onShow() {
		// 页面显示时刷新统计数据
		this.loadUserStats();
	},
	methods: {
		// 加载用户信息
		async loadUserInfo() {
			try {
				// 获取微信用户信息
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					this.userInfo = userInfo;
				} else {
					// 如果没有用户信息，尝试获取
					this.getUserProfile();
				}
			} catch (error) {
				console.error('加载用户信息失败:', error);
			}
		},
		
		// 获取用户资料
		getUserProfile() {
			uni.getUserProfile({
				desc: '用于完善用户资料',
				success: (res) => {
					this.userInfo = res.userInfo;
					uni.setStorageSync('userInfo', res.userInfo);
				},
				fail: (error) => {
					console.log('获取用户信息失败:', error);
				}
			});
		},
		
		// 加载用户统计数据
		async loadUserStats() {
			try {
				const res = await uniCloud.callFunction({
					name: 'getUserStats',
					data: {}
				});
				
				if (res.result.success) {
					this.userStats = res.result.data;
				}
			} catch (error) {
				console.error('加载用户统计失败:', error);
				// 设置默认值
				this.userStats = {
					publishCount: 0,
					collectCount: 0,
					viewCount: 0
				};
			}
		},
		
		// 检查管理员权限
		checkAdminAccess() {
			// 这里可以根据用户信息判断是否显示管理员入口
			// 简单示例：点击头像5次显示管理员入口
			let clickCount = uni.getStorageSync('avatarClickCount') || 0;
			this.showAdminEntry = clickCount >= 5;
		},
		
		// 编辑个人资料
		editProfile() {
			// 点击头像计数
			let clickCount = uni.getStorageSync('avatarClickCount') || 0;
			clickCount++;
			uni.setStorageSync('avatarClickCount', clickCount);
			
			if (clickCount >= 5) {
				this.showAdminEntry = true;
				uni.showToast({
					title: '管理员入口已开启',
					icon: 'none'
				});
			}
			
			// 获取用户信息
			this.getUserProfile();
		},
		
		// 跳转到我的发布
		goToMyPublish() {
			uni.navigateTo({
				url: '/pages/myPublish/myPublish'
			});
		},
		
		// 跳转到我的收藏
		goToMyCollect() {
			uni.navigateTo({
				url: '/pages/myCollect/myCollect'
			});
		},
		
		// 跳转到浏览记录
		goToViewHistory() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},
		
		// 跳转到设置
		goToSettings() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},
		
		// 跳转到帮助
		goToHelp() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},
		
		// 跳转到关于我们
		goToAbout() {
			uni.showModal({
				title: '关于我们',
				content: '房地产租赁小程序 v1.0\n为您提供便捷的房源租赁服务',
				showCancel: false
			});
		},
		
		// 跳转到管理后台
		goToAdmin() {
			uni.navigateTo({
				url: '/pages/admin/login'
			});
		}
	}
}
</script>

<style scoped>
.my-page {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 用户信息区域 */
.user-section {
	display: flex;
	align-items: center;
	padding: 40rpx 30rpx;
	background-color: #fff;
	margin-bottom: 20rpx;
}

.user-avatar {
	margin-right: 30rpx;
}

.avatar-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
}

.user-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.user-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.user-phone {
	font-size: 26rpx;
	color: #666;
}

.user-actions {
	display: flex;
	align-items: center;
}

.edit-btn {
	padding: 15rpx 30rpx;
	background-color: #f5f5f5;
	color: #666;
	border-radius: 25rpx;
	font-size: 26rpx;
}

/* 统计信息 */
.stats-section {
	display: flex;
	background-color: #fff;
	margin-bottom: 20rpx;
}

.stat-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx 20rpx;
	border-right: 1rpx solid #f5f5f5;
}

.stat-item:last-child {
	border-right: none;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #3cc51f;
	margin-bottom: 10rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

/* 菜单区域 */
.menu-section {
	background-color: #fff;
	margin-bottom: 20rpx;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-icon {
	width: 60rpx;
	font-size: 36rpx;
	margin-right: 30rpx;
}

.menu-text {
	flex: 1;
	font-size: 30rpx;
	color: #333;
}

.menu-arrow {
	font-size: 28rpx;
	color: #999;
}

/* 管理员入口 */
.admin-section {
	background-color: #fff;
}

.admin-entry {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	border-radius: 16rpx;
	margin: 0 30rpx 30rpx;
}

.admin-entry .menu-text,
.admin-entry .menu-arrow {
	color: #fff;
}
</style>
