<template>
	<view class="house-manage">
		<!-- 顶部操作栏 -->
		<view class="top-bar">
			<view class="search-section">
				<input v-model="searchKeyword" 
					   placeholder="搜索房源标题、地址..." 
					   class="search-input" 
					   @confirm="onSearch" />
				<text class="search-btn" @click="onSearch">搜索</text>
			</view>
			
			<view class="filter-tabs">
				<text class="filter-tab" 
					  :class="{ active: selectedStatus === '' }" 
					  @click="selectStatus('')">全部</text>
				<text class="filter-tab" 
					  :class="{ active: selectedStatus === 'pending' }" 
					  @click="selectStatus('pending')">待审核</text>
				<text class="filter-tab" 
					  :class="{ active: selectedStatus === 'approved' }" 
					  @click="selectStatus('approved')">已通过</text>
				<text class="filter-tab" 
					  :class="{ active: selectedStatus === 'rejected' }" 
					  @click="selectStatus('rejected')">已驳回</text>
			</view>
		</view>
		
		<!-- 房源列表 -->
		<view class="house-list">
			<view class="house-item" v-for="(house, index) in houseList" :key="house._id">
				<view class="house-header">
					<image :src="house.images[0]" mode="aspectFill" class="house-image"></image>
					<view class="house-info">
						<text class="house-title">{{ house.title }}</text>
						<text class="house-price">¥{{ house.price }}/月</text>
						<text class="house-location">{{ house.location.address }}</text>
						<text class="house-time">{{ formatTime(house.created_at) }}</text>
					</view>
					<view class="house-status">
						<text class="status-badge" :class="house.status">{{ getStatusText(house.status) }}</text>
					</view>
				</view>
				
				<view class="house-actions">
					<text class="action-btn view" @click="viewHouse(house._id)">查看</text>
					<text class="action-btn approve" 
						  v-if="house.status === 'pending'" 
						  @click="auditHouse(house._id, 'approved')">通过</text>
					<text class="action-btn reject" 
						  v-if="house.status === 'pending'" 
						  @click="showRejectModal(house._id)">驳回</text>
					<text class="action-btn delete" @click="deleteHouse(house._id)">删除</text>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore && !loading">
			<text class="load-text" @click="loadMore">加载更多</text>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="houseList.length === 0 && !loading">
			<text class="empty-text">暂无房源数据</text>
		</view>
		
		<!-- 驳回原因弹窗 -->
		<view class="modal-overlay" v-if="showRejectDialog" @click="hideRejectModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">驳回原因</text>
					<text class="modal-close" @click="hideRejectModal">×</text>
				</view>
				<view class="modal-body">
					<textarea v-model="rejectReason" 
							  placeholder="请输入驳回原因..." 
							  class="reject-textarea"></textarea>
				</view>
				<view class="modal-footer">
					<text class="modal-btn cancel" @click="hideRejectModal">取消</text>
					<text class="modal-btn confirm" @click="confirmReject">确认驳回</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchKeyword: '',
			selectedStatus: '',
			houseList: [],
			loading: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			showRejectDialog: false,
			rejectReason: '',
			currentRejectId: ''
		}
	},
	onLoad() {
		this.checkAuth();
		this.loadHouseList();
	},
	onPullDownRefresh() {
		this.page = 1;
		this.houseList = [];
		this.hasMore = true;
		this.loadHouseList();
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	onReachBottom() {
		if (this.hasMore && !this.loading) {
			this.loadMore();
		}
	},
	methods: {
		// 检查权限
		checkAuth() {
			const adminToken = uni.getStorageSync('adminToken');
			if (!adminToken) {
				uni.redirectTo({
					url: '/pages/admin/login'
				});
			}
		},
		
		// 加载房源列表
		async loadHouseList() {
			if (this.loading) return;
			
			this.loading = true;
			try {
				const res = await uniCloud.callFunction({
					name: 'getAdminHouseList',
					data: {
						page: this.page,
						pageSize: this.pageSize,
						keyword: this.searchKeyword,
						status: this.selectedStatus
					}
				});
				
				if (res.result.success) {
					const newList = res.result.data;
					if (this.page === 1) {
						this.houseList = newList;
					} else {
						this.houseList = [...this.houseList, ...newList];
					}
					
					this.hasMore = newList.length === this.pageSize;
				}
			} catch (error) {
				console.error('加载房源列表失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 搜索
		onSearch() {
			this.page = 1;
			this.houseList = [];
			this.hasMore = true;
			this.loadHouseList();
		},
		
		// 选择状态
		selectStatus(status) {
			this.selectedStatus = status;
			this.onSearch();
		},
		
		// 加载更多
		loadMore() {
			this.page++;
			this.loadHouseList();
		},
		
		// 查看房源
		viewHouse(houseId) {
			uni.navigateTo({
				url: `/pages/houseDetail/houseDetail?id=${houseId}`
			});
		},
		
		// 审核房源
		async auditHouse(houseId, status, reason = '') {
			try {
				const res = await uniCloud.callFunction({
					name: 'auditHouse',
					data: {
						houseId: houseId,
						status: status,
						reason: reason
					}
				});
				
				if (res.result.success) {
					uni.showToast({
						title: status === 'approved' ? '审核通过' : '已驳回',
						icon: 'success'
					});
					this.loadHouseList();
				}
			} catch (error) {
				console.error('审核失败:', error);
				uni.showToast({
					title: '操作失败',
					icon: 'none'
				});
			}
		},
		
		// 显示驳回弹窗
		showRejectModal(houseId) {
			this.currentRejectId = houseId;
			this.rejectReason = '';
			this.showRejectDialog = true;
		},
		
		// 隐藏驳回弹窗
		hideRejectModal() {
			this.showRejectDialog = false;
			this.currentRejectId = '';
			this.rejectReason = '';
		},
		
		// 确认驳回
		confirmReject() {
			if (!this.rejectReason.trim()) {
				uni.showToast({
					title: '请输入驳回原因',
					icon: 'none'
				});
				return;
			}
			
			this.auditHouse(this.currentRejectId, 'rejected', this.rejectReason);
			this.hideRejectModal();
		},
		
		// 删除房源
		deleteHouse(houseId) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个房源吗？删除后无法恢复。',
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await uniCloud.callFunction({
								name: 'deleteHouse',
								data: { houseId: houseId }
							});
							
							if (result.result.success) {
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
								this.loadHouseList();
							}
						} catch (error) {
							console.error('删除失败:', error);
							uni.showToast({
								title: '删除失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'pending': '待审核',
				'approved': '已通过',
				'rejected': '已驳回'
			};
			return statusMap[status] || '未知';
		},
		
		// 格式化时间
		formatTime(time) {
			const date = new Date(time);
			return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
		}
	}
}
</script>

<style scoped>
.house-manage {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 顶部操作栏 */
.top-bar {
	background-color: #fff;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
}

.search-section {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.search-input {
	flex: 1;
	padding: 20rpx;
	background-color: #f5f5f5;
	border-radius: 25rpx;
	font-size: 28rpx;
	margin-right: 20rpx;
}

.search-btn {
	padding: 20rpx 30rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 25rpx;
	font-size: 26rpx;
}

.filter-tabs {
	display: flex;
	gap: 30rpx;
}

.filter-tab {
	padding: 15rpx 25rpx;
	background-color: #f5f5f5;
	border-radius: 25rpx;
	font-size: 26rpx;
	color: #666;
}

.filter-tab.active {
	background-color: #3cc51f;
	color: #fff;
}

/* 房源列表 */
.house-list {
	padding: 30rpx;
}

.house-item {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.house-header {
	display: flex;
	margin-bottom: 20rpx;
}

.house-image {
	width: 150rpx;
	height: 120rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
}

.house-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.house-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.house-price {
	font-size: 28rpx;
	color: #ff6b35;
	font-weight: bold;
}

.house-location {
	font-size: 24rpx;
	color: #666;
}

.house-time {
	font-size: 22rpx;
	color: #999;
}

.house-status {
	display: flex;
	align-items: flex-start;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.status-badge.pending {
	background-color: #fff7e6;
	color: #fa8c16;
}

.status-badge.approved {
	background-color: #f6ffed;
	color: #52c41a;
}

.status-badge.rejected {
	background-color: #fff2f0;
	color: #ff4d4f;
}

.house-actions {
	display: flex;
	gap: 15rpx;
	flex-wrap: wrap;
}

.action-btn {
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #fff;
}

.action-btn.view {
	background-color: #1890ff;
}

.action-btn.approve {
	background-color: #52c41a;
}

.action-btn.reject {
	background-color: #ff4d4f;
}

.action-btn.delete {
	background-color: #f5222d;
}

/* 加载更多 */
.load-more {
	padding: 40rpx;
	text-align: center;
}

.load-text {
	color: #3cc51f;
	font-size: 28rpx;
}

/* 空状态 */
.empty-state {
	padding: 200rpx 40rpx;
	text-align: center;
}

.empty-text {
	color: #999;
	font-size: 28rpx;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	width: 600rpx;
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 48rpx;
	color: #999;
}

.modal-body {
	padding: 30rpx;
}

.reject-textarea {
	width: 100%;
	min-height: 200rpx;
	padding: 20rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
	font-size: 28rpx;
}

.modal-footer {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	border-top: 1rpx solid #eee;
}

.modal-btn {
	flex: 1;
	padding: 25rpx;
	text-align: center;
	border-radius: 12rpx;
	font-size: 28rpx;
}

.modal-btn.cancel {
	background-color: #f5f5f5;
	color: #666;
}

.modal-btn.confirm {
	background-color: #ff4d4f;
	color: #fff;
}
</style>
