'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	try {
		const userId = context.CLIENTIP; // 临时用IP作为用户标识
		
		// 获取用户收藏列表
		const collectResult = await db.collection('user_collect')
			.where({
				user_id: userId
			})
			.orderBy('collect_time', 'desc')
			.get();
		
		if (collectResult.data.length === 0) {
			return {
				success: true,
				data: []
			};
		}
		
		// 获取收藏的房源详情
		const houseIds = collectResult.data.map(item => item.house_id);
		const houseResult = await db.collection('house')
			.where({
				_id: db.command.in(houseIds),
				status: 'approved' // 只显示已审核通过的房源
			})
			.get();
		
		// 合并收藏时间信息
		const collectList = houseResult.data.map(house => {
			const collectInfo = collectResult.data.find(item => item.house_id === house._id);
			return {
				...house,
				collectTime: collectInfo ? collectInfo.collect_time : new Date(),
				images: house.images || ['/static/default-house.jpg']
			};
		});
		
		// 按收藏时间排序
		collectList.sort((a, b) => new Date(b.collectTime) - new Date(a.collectTime));
		
		return {
			success: true,
			data: collectList
		};
		
	} catch (error) {
		console.error('获取收藏列表失败:', error);
		return {
			success: false,
			message: '获取收藏列表失败',
			error: error.message
		};
	}
};
