<template>
	<view class="admin-settings">
		<!-- 公告设置 -->
		<view class="setting-section">
			<view class="section-title">公告管理</view>
			<view class="setting-item">
				<text class="setting-label">首页公告</text>
				<textarea v-model="settings.homeNotice" 
						  placeholder="请输入首页公告内容..." 
						  class="setting-textarea"></textarea>
			</view>
			<view class="setting-item">
				<text class="setting-label">公告状态</text>
				<switch :checked="settings.noticeEnabled" @change="onNoticeToggle" color="#3cc51f"></switch>
			</view>
		</view>
		
		<!-- 系统配置 -->
		<view class="setting-section">
			<view class="section-title">系统配置</view>
			<view class="setting-item">
				<text class="setting-label">房源审核</text>
				<switch :checked="settings.autoAudit" @change="onAutoAuditToggle" color="#3cc51f"></switch>
				<text class="setting-desc">开启后新发布的房源将自动通过审核</text>
			</view>
			<view class="setting-item">
				<text class="setting-label">用户注册</text>
				<switch :checked="settings.allowRegister" @change="onRegisterToggle" color="#3cc51f"></switch>
				<text class="setting-desc">关闭后将禁止新用户注册</text>
			</view>
			<view class="setting-item">
				<text class="setting-label">每日发布限制</text>
				<input v-model="settings.dailyPublishLimit" 
					   type="number" 
					   placeholder="0表示不限制" 
					   class="setting-input" />
				<text class="setting-desc">用户每日最多可发布的房源数量</text>
			</view>
		</view>
		
		<!-- 推荐设置 -->
		<view class="setting-section">
			<view class="section-title">推荐设置</view>
			<view class="setting-item">
				<text class="setting-label">推荐区域</text>
				<input v-model="settings.recommendAreas" 
					   placeholder="请输入推荐区域，用逗号分隔" 
					   class="setting-input" />
			</view>
			<view class="setting-item">
				<text class="setting-label">热门户型</text>
				<input v-model="settings.hotTypes" 
					   placeholder="请输入热门户型，用逗号分隔" 
					   class="setting-input" />
			</view>
		</view>
		
		<!-- 管理员设置 -->
		<view class="setting-section">
			<view class="section-title">管理员设置</view>
			<view class="setting-item">
				<text class="setting-label">修改密码</text>
				<button class="setting-btn" @click="showChangePassword">修改密码</button>
			</view>
			<view class="setting-item">
				<text class="setting-label">数据备份</text>
				<button class="setting-btn" @click="backupData">备份数据</button>
			</view>
			<view class="setting-item">
				<text class="setting-label">清理日志</text>
				<button class="setting-btn danger" @click="clearLogs">清理日志</button>
			</view>
		</view>
		
		<!-- 保存按钮 -->
		<view class="save-section">
			<button class="save-btn" @click="saveSettings" :disabled="saving">
				{{ saving ? '保存中...' : '保存设置' }}
			</button>
		</view>
		
		<!-- 修改密码弹窗 -->
		<view class="modal-overlay" v-if="showPasswordModal" @click="hidePasswordModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">修改密码</text>
					<text class="modal-close" @click="hidePasswordModal">×</text>
				</view>
				<view class="modal-body">
					<view class="form-item">
						<text class="form-label">当前密码</text>
						<input v-model="passwordForm.oldPassword" 
							   type="password" 
							   placeholder="请输入当前密码" 
							   class="form-input" />
					</view>
					<view class="form-item">
						<text class="form-label">新密码</text>
						<input v-model="passwordForm.newPassword" 
							   type="password" 
							   placeholder="请输入新密码" 
							   class="form-input" />
					</view>
					<view class="form-item">
						<text class="form-label">确认密码</text>
						<input v-model="passwordForm.confirmPassword" 
							   type="password" 
							   placeholder="请再次输入新密码" 
							   class="form-input" />
					</view>
				</view>
				<view class="modal-footer">
					<text class="modal-btn cancel" @click="hidePasswordModal">取消</text>
					<text class="modal-btn confirm" @click="changePassword">确认</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			settings: {
				homeNotice: '',
				noticeEnabled: true,
				autoAudit: false,
				allowRegister: true,
				dailyPublishLimit: 0,
				recommendAreas: '',
				hotTypes: ''
			},
			saving: false,
			showPasswordModal: false,
			passwordForm: {
				oldPassword: '',
				newPassword: '',
				confirmPassword: ''
			}
		}
	},
	onLoad() {
		this.checkAuth();
		this.loadSettings();
	},
	methods: {
		// 检查权限
		checkAuth() {
			const adminToken = uni.getStorageSync('adminToken');
			if (!adminToken) {
				uni.redirectTo({
					url: '/pages/admin/login'
				});
			}
		},
		
		// 加载设置
		async loadSettings() {
			try {
				const res = await uniCloud.callFunction({
					name: 'getSystemSettings',
					data: {}
				});
				
				if (res.result.success) {
					this.settings = { ...this.settings, ...res.result.data };
				}
			} catch (error) {
				console.error('加载设置失败:', error);
			}
		},
		
		// 公告开关
		onNoticeToggle(e) {
			this.settings.noticeEnabled = e.detail.value;
		},
		
		// 自动审核开关
		onAutoAuditToggle(e) {
			this.settings.autoAudit = e.detail.value;
		},
		
		// 注册开关
		onRegisterToggle(e) {
			this.settings.allowRegister = e.detail.value;
		},
		
		// 保存设置
		async saveSettings() {
			this.saving = true;
			try {
				const res = await uniCloud.callFunction({
					name: 'saveSystemSettings',
					data: this.settings
				});
				
				if (res.result.success) {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
				} else {
					uni.showToast({
						title: res.result.message || '保存失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('保存设置失败:', error);
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				});
			} finally {
				this.saving = false;
			}
		},
		
		// 显示修改密码弹窗
		showChangePassword() {
			this.passwordForm = {
				oldPassword: '',
				newPassword: '',
				confirmPassword: ''
			};
			this.showPasswordModal = true;
		},
		
		// 隐藏修改密码弹窗
		hidePasswordModal() {
			this.showPasswordModal = false;
		},
		
		// 修改密码
		async changePassword() {
			if (!this.passwordForm.oldPassword) {
				uni.showToast({
					title: '请输入当前密码',
					icon: 'none'
				});
				return;
			}
			
			if (!this.passwordForm.newPassword) {
				uni.showToast({
					title: '请输入新密码',
					icon: 'none'
				});
				return;
			}
			
			if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
				uni.showToast({
					title: '两次密码输入不一致',
					icon: 'none'
				});
				return;
			}
			
			try {
				const res = await uniCloud.callFunction({
					name: 'changeAdminPassword',
					data: {
						oldPassword: this.passwordForm.oldPassword,
						newPassword: this.passwordForm.newPassword
					}
				});
				
				if (res.result.success) {
					uni.showToast({
						title: '密码修改成功',
						icon: 'success'
					});
					this.hidePasswordModal();
				} else {
					uni.showToast({
						title: res.result.message || '密码修改失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('修改密码失败:', error);
				uni.showToast({
					title: '修改密码失败',
					icon: 'none'
				});
			}
		},
		
		// 备份数据
		async backupData() {
			uni.showLoading({ title: '备份中...' });
			try {
				const res = await uniCloud.callFunction({
					name: 'backupData',
					data: {}
				});
				
				if (res.result.success) {
					uni.showToast({
						title: '备份成功',
						icon: 'success'
					});
				}
			} catch (error) {
				console.error('备份失败:', error);
				uni.showToast({
					title: '备份失败',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},
		
		// 清理日志
		clearLogs() {
			uni.showModal({
				title: '确认清理',
				content: '确定要清理所有日志吗？此操作不可恢复。',
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await uniCloud.callFunction({
								name: 'clearLogs',
								data: {}
							});
							
							if (result.result.success) {
								uni.showToast({
									title: '清理成功',
									icon: 'success'
								});
							}
						} catch (error) {
							console.error('清理失败:', error);
							uni.showToast({
								title: '清理失败',
								icon: 'none'
							});
						}
					}
				}
			});
		}
	}
}
</script>

<style scoped>
.admin-settings {
	background-color: #f8f9fa;
	min-height: 100vh;
	padding: 30rpx;
}

/* 设置区域 */
.setting-section {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
	padding-bottom: 15rpx;
	border-bottom: 2rpx solid #f5f5f5;
}

.setting-item {
	margin-bottom: 30rpx;
}

.setting-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
}

.setting-input {
	width: 100%;
	padding: 20rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
	font-size: 28rpx;
	margin-bottom: 10rpx;
}

.setting-textarea {
	width: 100%;
	min-height: 150rpx;
	padding: 20rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
	font-size: 28rpx;
	margin-bottom: 10rpx;
}

.setting-desc {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

.setting-btn {
	padding: 20rpx 40rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 12rpx;
	font-size: 26rpx;
	border: none;
}

.setting-btn.danger {
	background-color: #ff4757;
}

/* 保存区域 */
.save-section {
	padding: 40rpx 0;
}

.save-btn {
	width: 100%;
	padding: 30rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 12rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
}

.save-btn[disabled] {
	background-color: #ccc;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	width: 600rpx;
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 48rpx;
	color: #999;
}

.modal-body {
	padding: 30rpx;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
}

.form-input {
	width: 100%;
	padding: 20rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
	font-size: 28rpx;
}

.modal-footer {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	border-top: 1rpx solid #eee;
}

.modal-btn {
	flex: 1;
	padding: 25rpx;
	text-align: center;
	border-radius: 12rpx;
	font-size: 28rpx;
}

.modal-btn.cancel {
	background-color: #f5f5f5;
	color: #666;
}

.modal-btn.confirm {
	background-color: #3cc51f;
	color: #fff;
}
</style>
