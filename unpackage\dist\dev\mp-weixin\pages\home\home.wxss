
.home.data-v-92bb8f34 {
	background-color: #f8f8f8;
	min-height: 100vh;
}

/* Banner样式 */
.banner.data-v-92bb8f34 {
	width: 100%;
	height: 400rpx;
}
.banner-image.data-v-92bb8f34 {
	width: 100%;
	height: 100%;
}

/* 搜索栏样式 */
.search-bar.data-v-92bb8f34 {
	padding: 20rpx;
	background-color: #fff;
}
.search-input.data-v-92bb8f34 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background-color: #f5f5f5;
	border-radius: 50rpx;
}
.search-placeholder.data-v-92bb8f34 {
	color: #999;
	font-size: 28rpx;
}
.search-icon.data-v-92bb8f34 {
	font-size: 32rpx;
}

/* 快捷导航样式 */
.quick-nav.data-v-92bb8f34 {
	display: flex;
	justify-content: space-around;
	padding: 40rpx 20rpx;
	background-color: #fff;
	margin-top: 20rpx;
}
.nav-item.data-v-92bb8f34 {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.nav-icon.data-v-92bb8f34 {
	font-size: 48rpx;
	margin-bottom: 10rpx;
}
.nav-text.data-v-92bb8f34 {
	font-size: 24rpx;
	color: #666;
}

/* 推荐房源样式 */
.recommend-section.data-v-92bb8f34 {
	margin-top: 20rpx;
	background-color: #fff;
	padding: 30rpx 20rpx;
}
.section-title.data-v-92bb8f34 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}
.title-text.data-v-92bb8f34 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.more-text.data-v-92bb8f34 {
	font-size: 28rpx;
	color: #3cc51f;
}
.house-list.data-v-92bb8f34 {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.house-item.data-v-92bb8f34 {
	display: flex;
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 16rpx;
}
.house-image.data-v-92bb8f34 {
	width: 200rpx;
	height: 150rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
}
.house-info.data-v-92bb8f34 {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.house-title.data-v-92bb8f34 {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}
.house-price.data-v-92bb8f34 {
	font-size: 32rpx;
	color: #ff6b35;
	font-weight: bold;
	margin-bottom: 10rpx;
}
.house-location.data-v-92bb8f34 {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 10rpx;
}
.house-tags.data-v-92bb8f34 {
	display: flex;
	gap: 10rpx;
}
.tag.data-v-92bb8f34 {
	padding: 4rpx 12rpx;
	background-color: #e8f4fd;
	color: #1890ff;
	font-size: 22rpx;
	border-radius: 8rpx;
}

/* 统计信息样式 */
.statistics.data-v-92bb8f34 {
	display: flex;
	justify-content: space-around;
	padding: 40rpx 20rpx;
	background-color: #fff;
	margin-top: 20rpx;
}
.stat-item.data-v-92bb8f34 {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.stat-number.data-v-92bb8f34 {
	font-size: 48rpx;
	font-weight: bold;
	color: #3cc51f;
	margin-bottom: 10rpx;
}
.stat-label.data-v-92bb8f34 {
	font-size: 24rpx;
	color: #666;
}

