'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const { houseId, reason } = event;
	
	if (!houseId || !reason) {
		return {
			success: false,
			message: '参数不完整'
		};
	}
	
	try {
		// 检查房源是否存在
		const houseResult = await db.collection('house').doc(houseId).get();
		
		if (houseResult.data.length === 0) {
			return {
				success: false,
				message: '房源不存在'
			};
		}
		
		const house = houseResult.data[0];
		const userId = context.CLIENTIP; // 临时用IP作为用户标识
		
		// 检查是否已经举报过
		const existingReport = await db.collection('house_reports')
			.where({
				house_id: houseId,
				reporter_id: userId
			})
			.get();
		
		if (existingReport.data.length > 0) {
			return {
				success: false,
				message: '您已经举报过这个房源了'
			};
		}
		
		// 记录举报信息
		const reportData = {
			house_id: houseId,
			house_title: house.title,
			reporter_id: userId,
			reason: reason,
			report_time: new Date(),
			status: 'pending', // 待处理
			ip: context.CLIENTIP
		};
		
		await db.collection('house_reports').add(reportData);
		
		// 增加房源举报次数
		await db.collection('house').doc(houseId).update({
			report_count: db.command.inc(1)
		});
		
		return {
			success: true,
			message: '举报成功，我们会尽快处理'
		};
		
	} catch (error) {
		console.error('举报房源失败:', error);
		return {
			success: false,
			message: '举报失败',
			error: error.message
		};
	}
};
