
.house-list-page.data-v-5fd6dcf8 {
	background-color: #f8f8f8;
	min-height: 100vh;
}

/* 搜索栏样式 */
.search-section.data-v-5fd6dcf8 {
	padding: 20rpx;
	background-color: #fff;
}
.search-input.data-v-5fd6dcf8 {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #f5f5f5;
	border-radius: 50rpx;
}
.search-input input.data-v-5fd6dcf8 {
	flex: 1;
	font-size: 28rpx;
}
.search-btn.data-v-5fd6dcf8 {
	padding: 10rpx 20rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 30rpx;
	font-size: 26rpx;
}

/* 筛选栏样式 */
.filter-section.data-v-5fd6dcf8 {
	background-color: #fff;
	border-bottom: 1rpx solid #eee;
}
.filter-scroll.data-v-5fd6dcf8 {
	white-space: nowrap;
	padding: 20rpx;
}
.filter-row.data-v-5fd6dcf8 {
	display: flex;
	padding: 10rpx 20rpx;
	border-top: 1rpx solid #f5f5f5;
}
.filter-item.data-v-5fd6dcf8 {
	display: inline-block;
	padding: 15rpx 30rpx;
	margin-right: 20rpx;
	background-color: #f5f5f5;
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #666;
}
.filter-item.active.data-v-5fd6dcf8 {
	background-color: #3cc51f;
	color: #fff;
}

/* 房源列表样式 */
.house-list.data-v-5fd6dcf8 {
	padding: 20rpx;
}
.house-item.data-v-5fd6dcf8 {
	display: flex;
	padding: 30rpx;
	background-color: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	position: relative;
}
.house-image.data-v-5fd6dcf8 {
	width: 200rpx;
	height: 150rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
}
.house-info.data-v-5fd6dcf8 {
	flex: 1;
}
.house-title.data-v-5fd6dcf8 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
}
.house-price.data-v-5fd6dcf8 {
	font-size: 36rpx;
	color: #ff6b35;
	font-weight: bold;
	margin-bottom: 15rpx;
}
.house-location.data-v-5fd6dcf8 {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 15rpx;
}
.house-config.data-v-5fd6dcf8 {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
	margin-bottom: 15rpx;
}
.config-item.data-v-5fd6dcf8 {
	padding: 4rpx 12rpx;
	background-color: #e8f4fd;
	color: #1890ff;
	font-size: 22rpx;
	border-radius: 8rpx;
}
.house-meta.data-v-5fd6dcf8 {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.publish-time.data-v-5fd6dcf8 {
	font-size: 24rpx;
	color: #999;
}
.house-type.data-v-5fd6dcf8 {
	padding: 4rpx 12rpx;
	background-color: #f0f0f0;
	color: #666;
	font-size: 22rpx;
	border-radius: 8rpx;
}
.house-actions.data-v-5fd6dcf8 {
	position: absolute;
	top: 30rpx;
	right: 30rpx;
}
.collect-btn.data-v-5fd6dcf8 {
	font-size: 48rpx;
}
.collect-btn.collected.data-v-5fd6dcf8 {
	color: #ff4757;
}

/* 加载更多样式 */
.load-more.data-v-5fd6dcf8 {
	padding: 40rpx;
	text-align: center;
}
.load-text.data-v-5fd6dcf8 {
	color: #3cc51f;
	font-size: 28rpx;
}

/* 空状态样式 */
.empty-state.data-v-5fd6dcf8 {
	padding: 200rpx 40rpx;
	text-align: center;
}
.empty-text.data-v-5fd6dcf8 {
	color: #999;
	font-size: 28rpx;
}

