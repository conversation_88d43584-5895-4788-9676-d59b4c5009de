<view class="my-page data-v-0be17cc6"><view class="user-section data-v-0be17cc6"><view class="user-avatar data-v-0be17cc6"><image class="avatar-image data-v-0be17cc6" src="{{userInfo.avatar||'/static/default-avatar.png'}}" mode="aspectFill" data-event-opts="{{[['tap',[['editProfile',['$event']]]]]}}" bindtap="__e"></image></view><view class="user-info data-v-0be17cc6"><text class="user-name data-v-0be17cc6">{{userInfo.nickname||'微信用户'}}</text><text class="user-phone data-v-0be17cc6">{{userInfo.phone||'未绑定手机'}}</text></view><view class="user-actions data-v-0be17cc6"><text data-event-opts="{{[['tap',[['editProfile',['$event']]]]]}}" class="edit-btn data-v-0be17cc6" bindtap="__e">编辑</text></view></view><view class="stats-section data-v-0be17cc6"><view data-event-opts="{{[['tap',[['goToMyPublish',['$event']]]]]}}" class="stat-item data-v-0be17cc6" bindtap="__e"><text class="stat-number data-v-0be17cc6">{{userStats.publishCount}}</text><text class="stat-label data-v-0be17cc6">我的发布</text></view><view data-event-opts="{{[['tap',[['goToMyCollect',['$event']]]]]}}" class="stat-item data-v-0be17cc6" bindtap="__e"><text class="stat-number data-v-0be17cc6">{{userStats.collectCount}}</text><text class="stat-label data-v-0be17cc6">我的收藏</text></view><view class="stat-item data-v-0be17cc6"><text class="stat-number data-v-0be17cc6">{{userStats.viewCount}}</text><text class="stat-label data-v-0be17cc6">浏览记录</text></view></view><view class="menu-section data-v-0be17cc6"><view data-event-opts="{{[['tap',[['goToMyPublish',['$event']]]]]}}" class="menu-item data-v-0be17cc6" bindtap="__e"><view class="menu-icon data-v-0be17cc6">📤</view><text class="menu-text data-v-0be17cc6">我的发布</text><text class="menu-arrow data-v-0be17cc6">></text></view><view data-event-opts="{{[['tap',[['goToMyCollect',['$event']]]]]}}" class="menu-item data-v-0be17cc6" bindtap="__e"><view class="menu-icon data-v-0be17cc6">❤️</view><text class="menu-text data-v-0be17cc6">我的收藏</text><text class="menu-arrow data-v-0be17cc6">></text></view><view data-event-opts="{{[['tap',[['goToViewHistory',['$event']]]]]}}" class="menu-item data-v-0be17cc6" bindtap="__e"><view class="menu-icon data-v-0be17cc6">👁️</view><text class="menu-text data-v-0be17cc6">浏览记录</text><text class="menu-arrow data-v-0be17cc6">></text></view></view><view class="menu-section data-v-0be17cc6"><view data-event-opts="{{[['tap',[['goToSettings',['$event']]]]]}}" class="menu-item data-v-0be17cc6" bindtap="__e"><view class="menu-icon data-v-0be17cc6">⚙️</view><text class="menu-text data-v-0be17cc6">设置</text><text class="menu-arrow data-v-0be17cc6">></text></view><view data-event-opts="{{[['tap',[['goToHelp',['$event']]]]]}}" class="menu-item data-v-0be17cc6" bindtap="__e"><view class="menu-icon data-v-0be17cc6">❓</view><text class="menu-text data-v-0be17cc6">帮助与反馈</text><text class="menu-arrow data-v-0be17cc6">></text></view><view data-event-opts="{{[['tap',[['goToAbout',['$event']]]]]}}" class="menu-item data-v-0be17cc6" bindtap="__e"><view class="menu-icon data-v-0be17cc6">ℹ️</view><text class="menu-text data-v-0be17cc6">关于我们</text><text class="menu-arrow data-v-0be17cc6">></text></view></view><block wx:if="{{showAdminEntry}}"><view class="admin-section data-v-0be17cc6"><view data-event-opts="{{[['tap',[['goToAdmin',['$event']]]]]}}" class="menu-item admin-entry data-v-0be17cc6" bindtap="__e"><view class="menu-icon data-v-0be17cc6">👨‍💼</view><text class="menu-text data-v-0be17cc6">管理后台</text><text class="menu-arrow data-v-0be17cc6">></text></view></view></block></view>