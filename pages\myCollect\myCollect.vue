<template>
	<view class="my-collect">
		<!-- 收藏列表 -->
		<view class="collect-list" v-if="collectList.length > 0">
			<view class="collect-item" v-for="(house, index) in collectList" :key="house._id">
				<image :src="house.images[0]" mode="aspectFill" class="house-image" @click="goToDetail(house._id)"></image>
				<view class="house-info" @click="goToDetail(house._id)">
					<text class="house-title">{{ house.title }}</text>
					<text class="house-price">¥{{ house.price }}/月</text>
					<text class="house-location">{{ house.location.address }}</text>
					<view class="house-tags">
						<text class="tag" v-for="(tag, tagIndex) in house.config.slice(0, 3)" :key="tagIndex">{{ tag }}</text>
					</view>
					<text class="collect-time">收藏于 {{ formatTime(house.collectTime) }}</text>
				</view>
				<view class="house-actions">
					<text class="action-btn contact" @click="contactOwner(house)">联系</text>
					<text class="action-btn uncollect" @click="uncollectHouse(house._id, index)">取消收藏</text>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-else-if="!loading">
			<image src="/static/empty-collect.png" mode="aspectFit" class="empty-image"></image>
			<text class="empty-text">暂无收藏的房源</text>
			<text class="empty-desc">去看看有什么好房源吧</text>
			<text class="go-browse-btn" @click="goToBrowse">去逛逛</text>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			collectList: [],
			loading: true
		}
	},
	onLoad() {
		this.loadCollectList();
	},
	onShow() {
		// 页面显示时刷新收藏列表
		this.loadCollectList();
	},
	onPullDownRefresh() {
		this.loadCollectList();
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	methods: {
		// 加载收藏列表
		async loadCollectList() {
			this.loading = true;
			try {
				const res = await uniCloud.callFunction({
					name: 'getMyCollectList',
					data: {}
				});
				
				if (res.result.success) {
					this.collectList = res.result.data;
				} else {
					uni.showToast({
						title: res.result.message || '加载失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('加载收藏列表失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 取消收藏
		async uncollectHouse(houseId, index) {
			uni.showModal({
				title: '确认取消',
				content: '确定要取消收藏这个房源吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await uniCloud.callFunction({
								name: 'toggleCollect',
								data: {
									houseId: houseId
								}
							});
							
							if (result.result.success) {
								// 从列表中移除
								this.collectList.splice(index, 1);
								uni.showToast({
									title: '取消收藏成功',
									icon: 'success'
								});
							}
						} catch (error) {
							console.error('取消收藏失败:', error);
							uni.showToast({
								title: '操作失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 联系房东
		contactOwner(house) {
			if (house.contact.phone) {
				uni.showActionSheet({
					itemList: ['拨打电话', '复制电话号码'],
					success: (res) => {
						if (res.tapIndex === 0) {
							uni.makePhoneCall({
								phoneNumber: house.contact.phone
							});
						} else if (res.tapIndex === 1) {
							uni.setClipboardData({
								data: house.contact.phone,
								success: () => {
									uni.showToast({
										title: '电话号码已复制',
										icon: 'none'
									});
								}
							});
						}
					}
				});
			} else if (house.contact.wechat) {
				uni.setClipboardData({
					data: house.contact.wechat,
					success: () => {
						uni.showToast({
							title: '微信号已复制',
							icon: 'none'
						});
					}
				});
			} else {
				uni.showToast({
					title: '暂无联系方式',
					icon: 'none'
				});
			}
		},
		
		// 跳转到房源详情
		goToDetail(houseId) {
			uni.navigateTo({
				url: `/pages/houseDetail/houseDetail?id=${houseId}`
			});
		},
		
		// 去浏览房源
		goToBrowse() {
			uni.switchTab({
				url: '/pages/houseList/houseList'
			});
		},
		
		// 格式化时间
		formatTime(time) {
			const date = new Date(time);
			const now = new Date();
			const diff = now - date;
			const days = Math.floor(diff / (1000 * 60 * 60 * 24));
			
			if (days === 0) {
				return '今天';
			} else if (days === 1) {
				return '昨天';
			} else if (days < 30) {
				return `${days}天前`;
			} else {
				return `${date.getMonth() + 1}-${date.getDate()}`;
			}
		}
	}
}
</script>

<style scoped>
.my-collect {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 收藏列表 */
.collect-list {
	padding: 30rpx;
}

.collect-item {
	display: flex;
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.house-image {
	width: 200rpx;
	height: 150rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
}

.house-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.house-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.house-price {
	font-size: 32rpx;
	color: #ff6b35;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.house-location {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.house-tags {
	display: flex;
	gap: 10rpx;
	margin-bottom: 10rpx;
}

.tag {
	padding: 4rpx 12rpx;
	background-color: #e8f4fd;
	color: #1890ff;
	font-size: 22rpx;
	border-radius: 8rpx;
}

.collect-time {
	font-size: 22rpx;
	color: #999;
}

.house-actions {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	margin-left: 20rpx;
}

.action-btn {
	padding: 15rpx 25rpx;
	border-radius: 25rpx;
	font-size: 24rpx;
	text-align: center;
	color: #fff;
}

.action-btn.contact {
	background-color: #3cc51f;
}

.action-btn.uncollect {
	background-color: #ff4757;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 200rpx 40rpx;
	text-align: center;
}

.empty-image {
	width: 300rpx;
	height: 300rpx;
	margin-bottom: 40rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 20rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #999;
	margin-bottom: 40rpx;
}

.go-browse-btn {
	padding: 25rpx 50rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 30rpx;
	font-size: 28rpx;
}

/* 加载状态 */
.loading-state {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 200rpx 40rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}
</style>
