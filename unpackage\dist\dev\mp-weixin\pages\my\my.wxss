
.my-page.data-v-0be17cc6 {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 用户信息区域 */
.user-section.data-v-0be17cc6 {
	display: flex;
	align-items: center;
	padding: 40rpx 30rpx;
	background-color: #fff;
	margin-bottom: 20rpx;
}
.user-avatar.data-v-0be17cc6 {
	margin-right: 30rpx;
}
.avatar-image.data-v-0be17cc6 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
}
.user-info.data-v-0be17cc6 {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}
.user-name.data-v-0be17cc6 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}
.user-phone.data-v-0be17cc6 {
	font-size: 26rpx;
	color: #666;
}
.user-actions.data-v-0be17cc6 {
	display: flex;
	align-items: center;
}
.edit-btn.data-v-0be17cc6 {
	padding: 15rpx 30rpx;
	background-color: #f5f5f5;
	color: #666;
	border-radius: 25rpx;
	font-size: 26rpx;
}

/* 统计信息 */
.stats-section.data-v-0be17cc6 {
	display: flex;
	background-color: #fff;
	margin-bottom: 20rpx;
}
.stat-item.data-v-0be17cc6 {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx 20rpx;
	border-right: 1rpx solid #f5f5f5;
}
.stat-item.data-v-0be17cc6:last-child {
	border-right: none;
}
.stat-number.data-v-0be17cc6 {
	font-size: 48rpx;
	font-weight: bold;
	color: #3cc51f;
	margin-bottom: 10rpx;
}
.stat-label.data-v-0be17cc6 {
	font-size: 24rpx;
	color: #666;
}

/* 菜单区域 */
.menu-section.data-v-0be17cc6 {
	background-color: #fff;
	margin-bottom: 20rpx;
}
.menu-item.data-v-0be17cc6 {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f5f5f5;
}
.menu-item.data-v-0be17cc6:last-child {
	border-bottom: none;
}
.menu-icon.data-v-0be17cc6 {
	width: 60rpx;
	font-size: 36rpx;
	margin-right: 30rpx;
}
.menu-text.data-v-0be17cc6 {
	flex: 1;
	font-size: 30rpx;
	color: #333;
}
.menu-arrow.data-v-0be17cc6 {
	font-size: 28rpx;
	color: #999;
}

/* 管理员入口 */
.admin-section.data-v-0be17cc6 {
	background-color: #fff;
}
.admin-entry.data-v-0be17cc6 {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	border-radius: 16rpx;
	margin: 0 30rpx 30rpx;
}
.admin-entry .menu-text.data-v-0be17cc6,
.admin-entry .menu-arrow.data-v-0be17cc6 {
	color: #fff;
}

