<template>
	<view class="house-detail" v-if="houseInfo">
		<!-- 图片轮播 -->
		<swiper class="image-swiper" indicator-dots="true" autoplay="false">
			<swiper-item v-for="(image, index) in houseInfo.images" :key="index">
				<image :src="image" mode="aspectFill" class="detail-image" @click="previewImage(index)"></image>
			</swiper-item>
		</swiper>
		
		<!-- 基本信息 -->
		<view class="basic-info">
			<view class="price-section">
				<text class="price">¥{{ houseInfo.price }}</text>
				<text class="price-unit">/月</text>
				<view class="collect-btn" @click="toggleCollect">
					<text class="collect-icon">{{ houseInfo.isCollected ? '❤️' : '🤍' }}</text>
					<text class="collect-text">{{ houseInfo.isCollected ? '已收藏' : '收藏' }}</text>
				</view>
			</view>
			
			<view class="title">{{ houseInfo.title }}</view>
			
			<view class="location">
				<text class="location-icon">📍</text>
				<text class="location-text">{{ houseInfo.location.address }}</text>
			</view>
			
			<view class="house-tags">
				<text class="tag type-tag">{{ houseInfo.type }}</text>
				<text class="tag status-tag" :class="houseInfo.status">{{ getStatusText(houseInfo.status) }}</text>
			</view>
		</view>
		
		<!-- 房源配置 -->
		<view class="config-section">
			<view class="section-title">房源配置</view>
			<view class="config-grid">
				<view class="config-item" v-for="(config, index) in houseInfo.config" :key="index">
					<text class="config-icon">{{ getConfigIcon(config) }}</text>
					<text class="config-text">{{ config }}</text>
				</view>
			</view>
		</view>
		
		<!-- 详细描述 -->
		<view class="description-section">
			<view class="section-title">房源描述</view>
			<text class="description-text">{{ houseInfo.desc }}</text>
		</view>
		
		<!-- 联系信息 -->
		<view class="contact-section">
			<view class="section-title">联系房东</view>
			<view class="contact-info">
				<view class="contact-item" v-if="houseInfo.contact.phone">
					<text class="contact-label">电话：</text>
					<text class="contact-value">{{ houseInfo.contact.phone }}</text>
					<text class="contact-btn" @click="makeCall">拨打</text>
				</view>
				<view class="contact-item" v-if="houseInfo.contact.wechat">
					<text class="contact-label">微信：</text>
					<text class="contact-value">{{ houseInfo.contact.wechat }}</text>
					<text class="contact-btn" @click="copyWechat">复制</text>
				</view>
			</view>
		</view>
		
		<!-- 举报按钮 -->
		<view class="report-section">
			<text class="report-btn" @click="reportHouse">举报房源</text>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<view class="action-btn secondary" @click="goBack">
				<text>返回</text>
			</view>
			<view class="action-btn primary" @click="contactOwner">
				<text>立即联系</text>
			</view>
		</view>
	</view>
	
	<!-- 加载状态 -->
	<view class="loading" v-else>
		<text>加载中...</text>
	</view>
</template>

<script>
export default {
	data() {
		return {
			houseId: '',
			houseInfo: null
		}
	},
	onLoad(options) {
		this.houseId = options.id;
		this.loadHouseDetail();
		this.recordVisit();
	},
	methods: {
		// 加载房源详情
		async loadHouseDetail() {
			try {
				const res = await uniCloud.callFunction({
					name: 'getHouseDetail',
					data: {
						houseId: this.houseId
					}
				});
				
				if (res.result.success) {
					this.houseInfo = res.result.data;
					// 设置页面标题
					uni.setNavigationBarTitle({
						title: this.houseInfo.title
					});
				} else {
					uni.showToast({
						title: '房源不存在',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}
			} catch (error) {
				console.error('加载房源详情失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			}
		},
		
		// 记录访问
		async recordVisit() {
			try {
				await uniCloud.callFunction({
					name: 'recordVisit',
					data: {
						houseId: this.houseId,
						page: 'detail'
					}
				});
			} catch (error) {
				console.error('记录访问失败:', error);
			}
		},
		
		// 预览图片
		previewImage(index) {
			uni.previewImage({
				urls: this.houseInfo.images,
				current: index
			});
		},
		
		// 切换收藏
		async toggleCollect() {
			try {
				const res = await uniCloud.callFunction({
					name: 'toggleCollect',
					data: {
						houseId: this.houseId
					}
				});
				
				if (res.result.success) {
					this.houseInfo.isCollected = !this.houseInfo.isCollected;
					uni.showToast({
						title: this.houseInfo.isCollected ? '收藏成功' : '取消收藏',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('收藏操作失败:', error);
			}
		},
		
		// 拨打电话
		makeCall() {
			uni.makePhoneCall({
				phoneNumber: this.houseInfo.contact.phone
			});
		},
		
		// 复制微信号
		copyWechat() {
			uni.setClipboardData({
				data: this.houseInfo.contact.wechat,
				success: () => {
					uni.showToast({
						title: '微信号已复制',
						icon: 'none'
					});
				}
			});
		},
		
		// 举报房源
		reportHouse() {
			uni.showActionSheet({
				itemList: ['虚假信息', '重复发布', '价格不符', '其他问题'],
				success: async (res) => {
					const reasons = ['虚假信息', '重复发布', '价格不符', '其他问题'];
					try {
						await uniCloud.callFunction({
							name: 'reportHouse',
							data: {
								houseId: this.houseId,
								reason: reasons[res.tapIndex]
							}
						});
						uni.showToast({
							title: '举报成功',
							icon: 'none'
						});
					} catch (error) {
						console.error('举报失败:', error);
					}
				}
			});
		},
		
		// 联系房东
		contactOwner() {
			if (this.houseInfo.contact.phone) {
				this.makeCall();
			} else if (this.houseInfo.contact.wechat) {
				this.copyWechat();
			} else {
				uni.showToast({
					title: '暂无联系方式',
					icon: 'none'
				});
			}
		},
		
		// 返回
		goBack() {
			uni.navigateBack();
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'pending': '待审核',
				'approved': '已发布',
				'rejected': '已驳回'
			};
			return statusMap[status] || '未知';
		},
		
		// 获取配置图标
		getConfigIcon(config) {
			const iconMap = {
				'空调': '❄️',
				'洗衣机': '🧺',
				'热水器': '🚿',
				'冰箱': '🧊',
				'电视': '📺',
				'WiFi': '📶',
				'床': '🛏️',
				'衣柜': '👔',
				'桌椅': '🪑',
				'阳台': '🌅'
			};
			return iconMap[config] || '✅';
		}
	}
}
</script>

<style scoped>
.house-detail {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 图片轮播样式 */
.image-swiper {
	width: 100%;
	height: 500rpx;
}

.detail-image {
	width: 100%;
	height: 100%;
}

/* 基本信息样式 */
.basic-info {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.price-section {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.price {
	font-size: 48rpx;
	color: #ff6b35;
	font-weight: bold;
}

.price-unit {
	font-size: 28rpx;
	color: #666;
	margin-left: 10rpx;
}

.collect-btn {
	display: flex;
	align-items: center;
	padding: 15rpx 25rpx;
	background-color: #f5f5f5;
	border-radius: 30rpx;
}

.collect-icon {
	font-size: 32rpx;
	margin-right: 10rpx;
}

.collect-text {
	font-size: 26rpx;
	color: #666;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.location {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.location-icon {
	font-size: 28rpx;
	margin-right: 10rpx;
}

.location-text {
	font-size: 28rpx;
	color: #666;
}

.house-tags {
	display: flex;
	gap: 15rpx;
}

.tag {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.type-tag {
	background-color: #e8f4fd;
	color: #1890ff;
}

.status-tag.approved {
	background-color: #f6ffed;
	color: #52c41a;
}

.status-tag.pending {
	background-color: #fff7e6;
	color: #fa8c16;
}

/* 配置区域样式 */
.config-section {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.config-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.config-item {
	display: flex;
	align-items: center;
	padding: 15rpx 20rpx;
	background-color: #f9f9f9;
	border-radius: 12rpx;
	min-width: 200rpx;
}

.config-icon {
	font-size: 32rpx;
	margin-right: 15rpx;
}

.config-text {
	font-size: 28rpx;
	color: #333;
}

/* 描述区域样式 */
.description-section {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.description-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

/* 联系信息样式 */
.contact-section {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.contact-info {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.contact-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 12rpx;
}

.contact-label {
	font-size: 28rpx;
	color: #666;
}

.contact-value {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	margin-left: 20rpx;
}

.contact-btn {
	padding: 10rpx 20rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 20rpx;
	font-size: 24rpx;
}

/* 举报区域样式 */
.report-section {
	padding: 30rpx;
	text-align: center;
}

.report-btn {
	color: #999;
	font-size: 26rpx;
	text-decoration: underline;
}

/* 底部操作栏样式 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	padding: 20rpx;
	background-color: #fff;
	border-top: 1rpx solid #eee;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	padding: 25rpx;
	text-align: center;
	border-radius: 12rpx;
	font-size: 32rpx;
}

.action-btn.secondary {
	background-color: #f5f5f5;
	color: #666;
}

.action-btn.primary {
	background-color: #3cc51f;
	color: #fff;
}

/* 加载状态样式 */
.loading {
	padding: 200rpx 40rpx;
	text-align: center;
	color: #999;
	font-size: 28rpx;
}
</style>
