'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const { houseId, page = 'list', duration = 0 } = event;
	
	try {
		// 获取用户信息
		const userId = context.CLIENTIP || 'anonymous'; // 临时用IP作为用户标识
		const userAgent = context.HEADERS['user-agent'] || '';
		
		// 记录访问日志
		const visitLog = {
			house_id: houseId || null,
			user_id: userId,
			page: page, // 访问页面：list, detail, home等
			visit_time: new Date(),
			duration: duration, // 停留时长（秒）
			user_agent: userAgent,
			ip: context.CLIENTIP
		};
		
		// 插入访问日志
		await db.collection('visit_logs').add(visitLog);
		
		// 如果是房源详情页，更新房源的访问次数
		if (houseId && page === 'detail') {
			await db.collection('house').doc(houseId).update({
				view_count: db.command.inc(1),
				last_visit_time: new Date()
			});
		}
		
		return {
			success: true,
			message: '访问记录成功'
		};
		
	} catch (error) {
		console.error('记录访问失败:', error);
		return {
			success: false,
			message: '记录访问失败',
			error: error.message
		};
	}
};
