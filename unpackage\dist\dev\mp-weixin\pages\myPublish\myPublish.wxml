<view class="my-publish data-v-0c16060e"><view class="status-tabs data-v-0c16060e"><text data-event-opts="{{[['tap',[['selectStatus',['']]]]]}}" class="{{['status-tab','data-v-0c16060e',(selectedStatus==='')?'active':'']}}" bindtap="__e">全部</text><text data-event-opts="{{[['tap',[['selectStatus',['pending']]]]]}}" class="{{['status-tab','data-v-0c16060e',(selectedStatus==='pending')?'active':'']}}" bindtap="__e">待审核</text><text data-event-opts="{{[['tap',[['selectStatus',['approved']]]]]}}" class="{{['status-tab','data-v-0c16060e',(selectedStatus==='approved')?'active':'']}}" bindtap="__e">已通过</text><text data-event-opts="{{[['tap',[['selectStatus',['rejected']]]]]}}" class="{{['status-tab','data-v-0c16060e',(selectedStatus==='rejected')?'active':'']}}" bindtap="__e">已驳回</text></view><block wx:if="{{$root.g0>0}}"><view class="publish-list data-v-0c16060e"><block wx:for="{{$root.l0}}" wx:for-item="house" wx:for-index="index" wx:key="_id"><view class="publish-item data-v-0c16060e"><image class="house-image data-v-0c16060e" src="{{house.$orig.images[0]}}" mode="aspectFill" data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['publishList','_id',house.$orig._id,'_id']]]]]]]}}" bindtap="__e"></image><view data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['publishList','_id',house.$orig._id,'_id']]]]]]]}}" class="house-info data-v-0c16060e" bindtap="__e"><text class="house-title data-v-0c16060e">{{house.$orig.title}}</text><text class="house-price data-v-0c16060e">{{"¥"+house.$orig.price+"/月"}}</text><text class="house-location data-v-0c16060e">{{house.$orig.location.address}}</text><view class="house-meta data-v-0c16060e"><text class="publish-time data-v-0c16060e">{{house.m0}}</text><text class="view-count data-v-0c16060e">{{"浏览 "+(house.$orig.view_count||0)+" 次"}}</text></view></view><view class="house-status data-v-0c16060e"><text class="{{['status-badge','data-v-0c16060e',house.$orig.status]}}">{{house.m1}}</text><view class="house-actions data-v-0c16060e"><block wx:if="{{house.$orig.status!=='approved'}}"><text data-event-opts="{{[['tap',[['editHouse',['$0'],[[['publishList','_id',house.$orig._id,'_id']]]]]]]}}" class="action-btn edit data-v-0c16060e" bindtap="__e">编辑</text></block><text data-event-opts="{{[['tap',[['deleteHouse',['$0',index],[[['publishList','_id',house.$orig._id,'_id']]]]]]]}}" class="action-btn delete data-v-0c16060e" bindtap="__e">删除</text></view></view></view></block></view></block><block wx:else><block wx:if="{{!loading}}"><view class="empty-state data-v-0c16060e"><image class="empty-image data-v-0c16060e" src="/static/empty-publish.png" mode="aspectFit"></image><text class="empty-text data-v-0c16060e">还没有发布过房源</text><text class="empty-desc data-v-0c16060e">发布您的第一个房源吧</text><text data-event-opts="{{[['tap',[['goToPublish',['$event']]]]]}}" class="go-publish-btn data-v-0c16060e" bindtap="__e">立即发布</text></view></block></block><block wx:if="{{loading}}"><view class="loading-state data-v-0c16060e"><text class="loading-text data-v-0c16060e">加载中...</text></view></block><block wx:if="{{showRejectReason}}"><view data-event-opts="{{[['tap',[['hideRejectReason',['$event']]]]]}}" class="modal-overlay data-v-0c16060e" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content data-v-0c16060e" catchtap="__e"><view class="modal-header data-v-0c16060e"><text class="modal-title data-v-0c16060e">驳回原因</text><text data-event-opts="{{[['tap',[['hideRejectReason',['$event']]]]]}}" class="modal-close data-v-0c16060e" bindtap="__e">×</text></view><view class="modal-body data-v-0c16060e"><text class="reject-reason data-v-0c16060e">{{currentRejectReason}}</text></view><view class="modal-footer data-v-0c16060e"><text data-event-opts="{{[['tap',[['hideRejectReason',['$event']]]]]}}" class="modal-btn data-v-0c16060e" bindtap="__e">知道了</text></view></view></view></block></view>