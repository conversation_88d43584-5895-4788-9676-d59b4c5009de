'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const { houseId } = event;
	
	if (!houseId) {
		return {
			success: false,
			message: '房源ID不能为空'
		};
	}
	
	try {
		// 检查房源是否存在
		const houseResult = await db.collection('house').doc(houseId).get();
		
		if (houseResult.data.length === 0) {
			return {
				success: false,
				message: '房源不存在'
			};
		}
		
		const house = houseResult.data[0];
		
		// 删除房源
		await db.collection('house').doc(houseId).remove();
		
		// 删除相关的收藏记录
		try {
			await db.collection('user_collect')
				.where({
					house_id: houseId
				})
				.remove();
		} catch (error) {
			console.log('删除收藏记录失败:', error);
		}
		
		// 删除相关的访问记录
		try {
			await db.collection('visit_logs')
				.where({
					house_id: houseId
				})
				.remove();
		} catch (error) {
			console.log('删除访问记录失败:', error);
		}
		
		// 删除相关的举报记录
		try {
			await db.collection('house_reports')
				.where({
					house_id: houseId
				})
				.remove();
		} catch (error) {
			console.log('删除举报记录失败:', error);
		}
		
		// 记录删除日志
		const deleteLog = {
			house_id: houseId,
			house_title: house.title,
			delete_time: new Date(),
			delete_by: 'admin',
			ip: context.CLIENTIP
		};
		
		await db.collection('delete_logs').add(deleteLog);
		
		return {
			success: true,
			message: '房源删除成功'
		};
		
	} catch (error) {
		console.error('删除房源失败:', error);
		return {
			success: false,
			message: '删除房源失败',
			error: error.message
		};
	}
};
