<template>
	<view class="home">
		<!-- Banner轮播图 -->
		<swiper class="banner" indicator-dots="true" autoplay="true" interval="3000" duration="500">
			<swiper-item v-for="(item, index) in bannerList" :key="index">
				<image :src="item.image" mode="aspectFill" class="banner-image"></image>
			</swiper-item>
		</swiper>
		
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input" @click="goToSearch">
				<text class="search-placeholder">搜索房源、位置、价格...</text>
				<text class="search-icon">🔍</text>
			</view>
		</view>
		
		<!-- 快捷入口 -->
		<view class="quick-nav">
			<view class="nav-item" @click="goToMap">
				<text class="nav-icon">📍</text>
				<text class="nav-text">地图找房</text>
			</view>
			<view class="nav-item" @click="goToPost">
				<text class="nav-icon">📤</text>
				<text class="nav-text">发布信息</text>
			</view>
			<view class="nav-item" @click="goToCollect">
				<text class="nav-icon">❤️</text>
				<text class="nav-text">我的收藏</text>
			</view>
			<view class="nav-item" @click="goToFilter">
				<text class="nav-icon">🏷️</text>
				<text class="nav-text">筛选房源</text>
			</view>
		</view>
		
		<!-- 推荐房源 -->
		<view class="recommend-section">
			<view class="section-title">
				<text class="title-text">推荐房源</text>
				<text class="more-text" @click="goToHouseList">更多 ></text>
			</view>
			<view class="house-list">
				<view class="house-item" v-for="(house, index) in recommendHouses" :key="index" @click="goToDetail(house._id)">
					<image :src="house.images[0]" mode="aspectFill" class="house-image"></image>
					<view class="house-info">
						<text class="house-title">{{ house.title }}</text>
						<text class="house-price">¥{{ house.price }}/月</text>
						<text class="house-location">{{ house.location.address }}</text>
						<view class="house-tags">
							<text class="tag" v-for="(tag, tagIndex) in house.config.slice(0, 3)" :key="tagIndex">{{ tag }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 统计信息 -->
		<view class="statistics">
			<view class="stat-item">
				<text class="stat-number">{{ statistics.todayPublish }}</text>
				<text class="stat-label">今日发布</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ statistics.todayVisit }}</text>
				<text class="stat-label">今日访问</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ statistics.totalHouse }}</text>
				<text class="stat-label">房源总数</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			bannerList: [
				{ image: '/static/banner1.jpg' },
				{ image: '/static/banner2.jpg' },
				{ image: '/static/banner3.jpg' }
			],
			recommendHouses: [],
			statistics: {
				todayPublish: 0,
				todayVisit: 0,
				totalHouse: 0
			}
		}
	},
	onLoad() {
		this.loadRecommendHouses();
		this.loadStatistics();
	},
	onPullDownRefresh() {
		this.loadRecommendHouses();
		this.loadStatistics();
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	methods: {
		// 加载推荐房源
		async loadRecommendHouses() {
			try {
				const res = await uniCloud.callFunction({
					name: 'getHouseList',
					data: {
						pageSize: 6,
						recommend: true
					}
				});
				if (res.result.success) {
					this.recommendHouses = res.result.data;
				}
			} catch (error) {
				console.error('加载推荐房源失败:', error);
			}
		},
		
		// 加载统计数据
		async loadStatistics() {
			try {
				const res = await uniCloud.callFunction({
					name: 'getStatistics',
					data: {}
				});
				if (res.result.success) {
					this.statistics = res.result.data;
				}
			} catch (error) {
				console.error('加载统计数据失败:', error);
			}
		},
		
		// 跳转到搜索页面
		goToSearch() {
			uni.navigateTo({
				url: '/pages/houseList/houseList?search=true'
			});
		},
		
		// 跳转到地图找房
		goToMap() {
			uni.showToast({
				title: '地图找房功能开发中',
				icon: 'none'
			});
		},
		
		// 跳转到发布页面
		goToPost() {
			uni.switchTab({
				url: '/pages/postHouse/postHouse'
			});
		},
		
		// 跳转到收藏页面
		goToCollect() {
			uni.navigateTo({
				url: '/pages/myCollect/myCollect'
			});
		},
		
		// 跳转到筛选页面
		goToFilter() {
			uni.navigateTo({
				url: '/pages/houseList/houseList?filter=true'
			});
		},
		
		// 跳转到房源列表
		goToHouseList() {
			uni.switchTab({
				url: '/pages/houseList/houseList'
			});
		},
		
		// 跳转到房源详情
		goToDetail(houseId) {
			uni.navigateTo({
				url: `/pages/houseDetail/houseDetail?id=${houseId}`
			});
		}
	}
}
</script>

<style scoped>
.home {
	background-color: #f8f8f8;
	min-height: 100vh;
}

/* Banner样式 */
.banner {
	width: 100%;
	height: 400rpx;
}

.banner-image {
	width: 100%;
	height: 100%;
}

/* 搜索栏样式 */
.search-bar {
	padding: 20rpx;
	background-color: #fff;
}

.search-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background-color: #f5f5f5;
	border-radius: 50rpx;
}

.search-placeholder {
	color: #999;
	font-size: 28rpx;
}

.search-icon {
	font-size: 32rpx;
}

/* 快捷导航样式 */
.quick-nav {
	display: flex;
	justify-content: space-around;
	padding: 40rpx 20rpx;
	background-color: #fff;
	margin-top: 20rpx;
}

.nav-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.nav-icon {
	font-size: 48rpx;
	margin-bottom: 10rpx;
}

.nav-text {
	font-size: 24rpx;
	color: #666;
}

/* 推荐房源样式 */
.recommend-section {
	margin-top: 20rpx;
	background-color: #fff;
	padding: 30rpx 20rpx;
}

.section-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.more-text {
	font-size: 28rpx;
	color: #3cc51f;
}

.house-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.house-item {
	display: flex;
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 16rpx;
}

.house-image {
	width: 200rpx;
	height: 150rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
}

.house-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.house-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.house-price {
	font-size: 32rpx;
	color: #ff6b35;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.house-location {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.house-tags {
	display: flex;
	gap: 10rpx;
}

.tag {
	padding: 4rpx 12rpx;
	background-color: #e8f4fd;
	color: #1890ff;
	font-size: 22rpx;
	border-radius: 8rpx;
}

/* 统计信息样式 */
.statistics {
	display: flex;
	justify-content: space-around;
	padding: 40rpx 20rpx;
	background-color: #fff;
	margin-top: 20rpx;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #3cc51f;
	margin-bottom: 10rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}
</style>
