'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	try {
		const clearTime = new Date();
		const logCollections = [
			'visit_logs',
			'audit_logs', 
			'delete_logs',
			'publish_logs',
			'user_ban_logs',
			'user_delete_logs',
			'settings_change_logs',
			'admin_password_logs'
		];
		
		const clearStats = {};
		let totalCleared = 0;
		
		// 清理各个日志集合
		for (const collectionName of logCollections) {
			try {
				// 先统计数量
				const countResult = await db.collection(collectionName).count();
				const count = countResult.total;
				
				if (count > 0) {
					// 保留最近30天的日志，删除更早的
					const thirtyDaysAgo = new Date();
					thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
					
					// 根据不同集合的时间字段名进行删除
					let timeField = 'created_at';
					if (collectionName === 'visit_logs') timeField = 'visit_time';
					if (collectionName === 'audit_logs') timeField = 'audit_time';
					if (collectionName === 'delete_logs') timeField = 'delete_time';
					if (collectionName === 'publish_logs') timeField = 'publish_time';
					if (collectionName === 'user_ban_logs') timeField = 'ban_time';
					if (collectionName === 'user_delete_logs') timeField = 'delete_time';
					if (collectionName === 'settings_change_logs') timeField = 'change_time';
					if (collectionName === 'admin_password_logs') timeField = 'change_time';
					
					// 删除30天前的记录
					const deleteResult = await db.collection(collectionName)
						.where({
							[timeField]: db.command.lt(thirtyDaysAgo)
						})
						.remove();
					
					clearStats[collectionName] = {
						total: count,
						cleared: deleteResult.deleted || 0
					};
					totalCleared += deleteResult.deleted || 0;
				} else {
					clearStats[collectionName] = {
						total: 0,
						cleared: 0
					};
				}
			} catch (error) {
				console.log(`清理集合 ${collectionName} 失败:`, error);
				clearStats[collectionName] = {
					total: 0,
					cleared: 0,
					error: error.message
				};
			}
		}
		
		// 记录清理日志
		const clearLog = {
			clear_time: clearTime,
			clear_stats: clearStats,
			total_cleared: totalCleared,
			admin_ip: context.CLIENTIP,
			clear_type: 'manual'
		};
		
		await db.collection('log_clear_records').add(clearLog);
		
		return {
			success: true,
			message: `日志清理成功，共清理 ${totalCleared} 条记录`,
			data: {
				clearTime: clearTime,
				totalCleared: totalCleared,
				stats: clearStats
			}
		};
		
	} catch (error) {
		console.error('清理日志失败:', error);
		
		// 记录清理失败日志
		try {
			await db.collection('log_clear_records').add({
				clear_time: new Date(),
				admin_ip: context.CLIENTIP,
				clear_type: 'manual',
				status: 'failed',
				error_message: error.message
			});
		} catch (logError) {
			console.error('记录清理失败日志失败:', logError);
		}
		
		return {
			success: false,
			message: '清理日志失败',
			error: error.message
		};
	}
};
