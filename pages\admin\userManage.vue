<template>
	<view class="user-manage">
		<!-- 顶部操作栏 -->
		<view class="top-bar">
			<view class="search-section">
				<input v-model="searchKeyword" 
					   placeholder="搜索用户昵称、手机号..." 
					   class="search-input" 
					   @confirm="onSearch" />
				<text class="search-btn" @click="onSearch">搜索</text>
			</view>
			
			<view class="filter-tabs">
				<text class="filter-tab" 
					  :class="{ active: selectedStatus === '' }" 
					  @click="selectStatus('')">全部</text>
				<text class="filter-tab" 
					  :class="{ active: selectedStatus === 'active' }" 
					  @click="selectStatus('active')">正常</text>
				<text class="filter-tab" 
					  :class="{ active: selectedStatus === 'banned' }" 
					  @click="selectStatus('banned')">已封禁</text>
			</view>
		</view>
		
		<!-- 用户列表 -->
		<view class="user-list">
			<view class="user-item" v-for="(user, index) in userList" :key="user._id">
				<view class="user-header">
					<image :src="user.avatar || '/static/default-avatar.png'" mode="aspectFill" class="user-avatar"></image>
					<view class="user-info">
						<text class="user-nickname">{{ user.nickname || '微信用户' }}</text>
						<text class="user-phone">{{ user.phone || '未绑定手机' }}</text>
						<text class="user-time">注册时间：{{ formatTime(user.created_at) }}</text>
						<view class="user-stats">
							<text class="stat-item">发布：{{ user.publishCount || 0 }}</text>
							<text class="stat-item">收藏：{{ user.collectCount || 0 }}</text>
						</view>
					</view>
					<view class="user-status">
						<text class="status-badge" :class="user.status || 'active'">{{ getStatusText(user.status) }}</text>
					</view>
				</view>
				
				<view class="user-actions">
					<text class="action-btn view" @click="viewUserDetail(user._id)">查看</text>
					<text class="action-btn ban" 
						  v-if="(user.status || 'active') === 'active'" 
						  @click="banUser(user._id, index)">封禁</text>
					<text class="action-btn unban" 
						  v-if="user.status === 'banned'" 
						  @click="unbanUser(user._id, index)">解封</text>
					<text class="action-btn delete" @click="deleteUser(user._id, index)">删除</text>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore && !loading">
			<text class="load-text" @click="loadMore">加载更多</text>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="userList.length === 0 && !loading">
			<text class="empty-text">暂无用户数据</text>
		</view>
		
		<!-- 用户详情弹窗 -->
		<view class="modal-overlay" v-if="showUserDetail" @click="hideUserDetail">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">用户详情</text>
					<text class="modal-close" @click="hideUserDetail">×</text>
				</view>
				<view class="modal-body" v-if="currentUser">
					<view class="detail-item">
						<text class="detail-label">昵称：</text>
						<text class="detail-value">{{ currentUser.nickname || '微信用户' }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">手机：</text>
						<text class="detail-value">{{ currentUser.phone || '未绑定' }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">注册时间：</text>
						<text class="detail-value">{{ formatTime(currentUser.created_at) }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">发布房源：</text>
						<text class="detail-value">{{ currentUser.publishCount || 0 }} 个</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">收藏房源：</text>
						<text class="detail-value">{{ currentUser.collectCount || 0 }} 个</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">状态：</text>
						<text class="detail-value" :class="currentUser.status || 'active'">{{ getStatusText(currentUser.status) }}</text>
					</view>
				</view>
				<view class="modal-footer">
					<text class="modal-btn" @click="hideUserDetail">关闭</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchKeyword: '',
			selectedStatus: '',
			userList: [],
			loading: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			showUserDetail: false,
			currentUser: null
		}
	},
	onLoad() {
		this.checkAuth();
		this.loadUserList();
	},
	onPullDownRefresh() {
		this.page = 1;
		this.userList = [];
		this.hasMore = true;
		this.loadUserList();
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	onReachBottom() {
		if (this.hasMore && !this.loading) {
			this.loadMore();
		}
	},
	methods: {
		// 检查权限
		checkAuth() {
			const adminToken = uni.getStorageSync('adminToken');
			if (!adminToken) {
				uni.redirectTo({
					url: '/pages/admin/login'
				});
			}
		},
		
		// 加载用户列表
		async loadUserList() {
			if (this.loading) return;
			
			this.loading = true;
			try {
				const res = await uniCloud.callFunction({
					name: 'getUserList',
					data: {
						page: this.page,
						pageSize: this.pageSize,
						keyword: this.searchKeyword,
						status: this.selectedStatus
					}
				});
				
				if (res.result.success) {
					const newList = res.result.data;
					if (this.page === 1) {
						this.userList = newList;
					} else {
						this.userList = [...this.userList, ...newList];
					}
					
					this.hasMore = newList.length === this.pageSize;
				}
			} catch (error) {
				console.error('加载用户列表失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 搜索
		onSearch() {
			this.page = 1;
			this.userList = [];
			this.hasMore = true;
			this.loadUserList();
		},
		
		// 选择状态
		selectStatus(status) {
			this.selectedStatus = status;
			this.onSearch();
		},
		
		// 加载更多
		loadMore() {
			this.page++;
			this.loadUserList();
		},
		
		// 查看用户详情
		async viewUserDetail(userId) {
			try {
				const res = await uniCloud.callFunction({
					name: 'getUserDetail',
					data: { userId: userId }
				});
				
				if (res.result.success) {
					this.currentUser = res.result.data;
					this.showUserDetail = true;
				}
			} catch (error) {
				console.error('获取用户详情失败:', error);
			}
		},
		
		// 隐藏用户详情
		hideUserDetail() {
			this.showUserDetail = false;
			this.currentUser = null;
		},
		
		// 封禁用户
		banUser(userId, index) {
			uni.showModal({
				title: '确认封禁',
				content: '确定要封禁这个用户吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await uniCloud.callFunction({
								name: 'banUser',
								data: { userId: userId }
							});
							
							if (result.result.success) {
								this.userList[index].status = 'banned';
								uni.showToast({
									title: '封禁成功',
									icon: 'success'
								});
							}
						} catch (error) {
							console.error('封禁失败:', error);
							uni.showToast({
								title: '操作失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 解封用户
		unbanUser(userId, index) {
			uni.showModal({
				title: '确认解封',
				content: '确定要解封这个用户吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await uniCloud.callFunction({
								name: 'unbanUser',
								data: { userId: userId }
							});
							
							if (result.result.success) {
								this.userList[index].status = 'active';
								uni.showToast({
									title: '解封成功',
									icon: 'success'
								});
							}
						} catch (error) {
							console.error('解封失败:', error);
							uni.showToast({
								title: '操作失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 删除用户
		deleteUser(userId, index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个用户吗？删除后无法恢复。',
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await uniCloud.callFunction({
								name: 'deleteUser',
								data: { userId: userId }
							});
							
							if (result.result.success) {
								this.userList.splice(index, 1);
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
							}
						} catch (error) {
							console.error('删除失败:', error);
							uni.showToast({
								title: '删除失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'active': '正常',
				'banned': '已封禁'
			};
			return statusMap[status || 'active'] || '正常';
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return '未知';
			const date = new Date(time);
			return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
		}
	}
}
</script>

<style scoped>
.user-manage {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 顶部操作栏 */
.top-bar {
	background-color: #fff;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
}

.search-section {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.search-input {
	flex: 1;
	padding: 20rpx;
	background-color: #f5f5f5;
	border-radius: 25rpx;
	font-size: 28rpx;
	margin-right: 20rpx;
}

.search-btn {
	padding: 20rpx 30rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 25rpx;
	font-size: 26rpx;
}

.filter-tabs {
	display: flex;
	gap: 30rpx;
}

.filter-tab {
	padding: 15rpx 25rpx;
	background-color: #f5f5f5;
	border-radius: 25rpx;
	font-size: 26rpx;
	color: #666;
}

.filter-tab.active {
	background-color: #3cc51f;
	color: #fff;
}

/* 用户列表 */
.user-list {
	padding: 30rpx;
}

.user-item {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.user-header {
	display: flex;
	margin-bottom: 20rpx;
}

.user-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	margin-right: 20rpx;
}

.user-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.user-nickname {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.user-phone {
	font-size: 26rpx;
	color: #666;
}

.user-time {
	font-size: 24rpx;
	color: #999;
}

.user-stats {
	display: flex;
	gap: 20rpx;
}

.stat-item {
	font-size: 22rpx;
	color: #666;
}

.user-status {
	display: flex;
	align-items: flex-start;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.status-badge.active {
	background-color: #f6ffed;
	color: #52c41a;
}

.status-badge.banned {
	background-color: #fff2f0;
	color: #ff4d4f;
}

.user-actions {
	display: flex;
	gap: 15rpx;
	flex-wrap: wrap;
}

.action-btn {
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #fff;
}

.action-btn.view {
	background-color: #1890ff;
}

.action-btn.ban {
	background-color: #fa8c16;
}

.action-btn.unban {
	background-color: #52c41a;
}

.action-btn.delete {
	background-color: #f5222d;
}

/* 加载更多 */
.load-more {
	padding: 40rpx;
	text-align: center;
}

.load-text {
	color: #3cc51f;
	font-size: 28rpx;
}

/* 空状态 */
.empty-state {
	padding: 200rpx 40rpx;
	text-align: center;
}

.empty-text {
	color: #999;
	font-size: 28rpx;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	width: 600rpx;
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 48rpx;
	color: #999;
}

.modal-body {
	padding: 30rpx;
}

.detail-item {
	display: flex;
	margin-bottom: 20rpx;
}

.detail-label {
	width: 150rpx;
	font-size: 28rpx;
	color: #666;
}

.detail-value {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.detail-value.active {
	color: #52c41a;
}

.detail-value.banned {
	color: #ff4d4f;
}

.modal-footer {
	padding: 30rpx;
	border-top: 1rpx solid #eee;
}

.modal-btn {
	width: 100%;
	padding: 25rpx;
	text-align: center;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 12rpx;
	font-size: 28rpx;
}
</style>
