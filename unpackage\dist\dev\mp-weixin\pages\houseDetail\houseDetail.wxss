
.house-detail.data-v-6992ea2a {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 图片轮播样式 */
.image-swiper.data-v-6992ea2a {
	width: 100%;
	height: 500rpx;
}
.detail-image.data-v-6992ea2a {
	width: 100%;
	height: 100%;
}

/* 基本信息样式 */
.basic-info.data-v-6992ea2a {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.price-section.data-v-6992ea2a {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}
.price.data-v-6992ea2a {
	font-size: 48rpx;
	color: #ff6b35;
	font-weight: bold;
}
.price-unit.data-v-6992ea2a {
	font-size: 28rpx;
	color: #666;
	margin-left: 10rpx;
}
.collect-btn.data-v-6992ea2a {
	display: flex;
	align-items: center;
	padding: 15rpx 25rpx;
	background-color: #f5f5f5;
	border-radius: 30rpx;
}
.collect-icon.data-v-6992ea2a {
	font-size: 32rpx;
	margin-right: 10rpx;
}
.collect-text.data-v-6992ea2a {
	font-size: 26rpx;
	color: #666;
}
.title.data-v-6992ea2a {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}
.location.data-v-6992ea2a {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.location-icon.data-v-6992ea2a {
	font-size: 28rpx;
	margin-right: 10rpx;
}
.location-text.data-v-6992ea2a {
	font-size: 28rpx;
	color: #666;
}
.house-tags.data-v-6992ea2a {
	display: flex;
	gap: 15rpx;
}
.tag.data-v-6992ea2a {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}
.type-tag.data-v-6992ea2a {
	background-color: #e8f4fd;
	color: #1890ff;
}
.status-tag.approved.data-v-6992ea2a {
	background-color: #f6ffed;
	color: #52c41a;
}
.status-tag.pending.data-v-6992ea2a {
	background-color: #fff7e6;
	color: #fa8c16;
}

/* 配置区域样式 */
.config-section.data-v-6992ea2a {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.section-title.data-v-6992ea2a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}
.config-grid.data-v-6992ea2a {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}
.config-item.data-v-6992ea2a {
	display: flex;
	align-items: center;
	padding: 15rpx 20rpx;
	background-color: #f9f9f9;
	border-radius: 12rpx;
	min-width: 200rpx;
}
.config-icon.data-v-6992ea2a {
	font-size: 32rpx;
	margin-right: 15rpx;
}
.config-text.data-v-6992ea2a {
	font-size: 28rpx;
	color: #333;
}

/* 描述区域样式 */
.description-section.data-v-6992ea2a {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.description-text.data-v-6992ea2a {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

/* 联系信息样式 */
.contact-section.data-v-6992ea2a {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.contact-info.data-v-6992ea2a {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.contact-item.data-v-6992ea2a {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 12rpx;
}
.contact-label.data-v-6992ea2a {
	font-size: 28rpx;
	color: #666;
}
.contact-value.data-v-6992ea2a {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	margin-left: 20rpx;
}
.contact-btn.data-v-6992ea2a {
	padding: 10rpx 20rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 20rpx;
	font-size: 24rpx;
}

/* 举报区域样式 */
.report-section.data-v-6992ea2a {
	padding: 30rpx;
	text-align: center;
}
.report-btn.data-v-6992ea2a {
	color: #999;
	font-size: 26rpx;
	text-decoration: underline;
}

/* 底部操作栏样式 */
.bottom-actions.data-v-6992ea2a {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	padding: 20rpx;
	background-color: #fff;
	border-top: 1rpx solid #eee;
	gap: 20rpx;
}
.action-btn.data-v-6992ea2a {
	flex: 1;
	padding: 25rpx;
	text-align: center;
	border-radius: 12rpx;
	font-size: 32rpx;
}
.action-btn.secondary.data-v-6992ea2a {
	background-color: #f5f5f5;
	color: #666;
}
.action-btn.primary.data-v-6992ea2a {
	background-color: #3cc51f;
	color: #fff;
}

/* 加载状态样式 */
.loading.data-v-6992ea2a {
	padding: 200rpx 40rpx;
	text-align: center;
	color: #999;
	font-size: 28rpx;
}

