<template>
	<view class="house-list-page">
		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-input">
				<input v-model="searchKeyword" placeholder="搜索房源、位置..." @confirm="onSearch" />
				<text class="search-btn" @click="onSearch">搜索</text>
			</view>
		</view>
		
		<!-- 筛选栏 -->
		<view class="filter-section">
			<scroll-view class="filter-scroll" scroll-x="true">
				<view class="filter-item" :class="{ active: selectedArea === '' }" @click="selectArea('')">
					<text>全部区域</text>
				</view>
				<view class="filter-item" :class="{ active: selectedArea === item }" 
					  v-for="item in areaList" :key="item" @click="selectArea(item)">
					<text>{{ item }}</text>
				</view>
			</scroll-view>
			
			<view class="filter-row">
				<view class="filter-item" :class="{ active: selectedPrice === '' }" @click="selectPrice('')">
					<text>价格</text>
				</view>
				<view class="filter-item" :class="{ active: selectedPrice === item.value }" 
					  v-for="item in priceList" :key="item.value" @click="selectPrice(item.value)">
					<text>{{ item.label }}</text>
				</view>
			</view>
			
			<view class="filter-row">
				<view class="filter-item" :class="{ active: selectedType === '' }" @click="selectType('')">
					<text>户型</text>
				</view>
				<view class="filter-item" :class="{ active: selectedType === item }" 
					  v-for="item in typeList" :key="item" @click="selectType(item)">
					<text>{{ item }}</text>
				</view>
			</view>
		</view>
		
		<!-- 房源列表 -->
		<view class="house-list">
			<view class="house-item" v-for="(house, index) in houseList" :key="house._id" @click="goToDetail(house._id)">
				<image :src="house.images[0]" mode="aspectFill" class="house-image"></image>
				<view class="house-info">
					<view class="house-title">{{ house.title }}</view>
					<view class="house-price">¥{{ house.price }}/月</view>
					<view class="house-location">{{ house.location.address }}</view>
					<view class="house-config">
						<text class="config-item" v-for="(config, configIndex) in house.config.slice(0, 4)" :key="configIndex">
							{{ config }}
						</text>
					</view>
					<view class="house-meta">
						<text class="publish-time">{{ formatTime(house.created_at) }}</text>
						<text class="house-type">{{ house.type }}</text>
					</view>
				</view>
				<view class="house-actions">
					<text class="collect-btn" :class="{ collected: house.isCollected }" @click.stop="toggleCollect(house, index)">
						{{ house.isCollected ? '❤️' : '🤍' }}
					</text>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore">
			<text class="load-text" @click="loadMore">加载更多</text>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="houseList.length === 0 && !loading">
			<text class="empty-text">暂无房源信息</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchKeyword: '',
			selectedArea: '',
			selectedPrice: '',
			selectedType: '',
			houseList: [],
			loading: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			areaList: ['市中心', '大学城', '高新区', '老城区', '新区'],
			priceList: [
				{ label: '1000以下', value: '0-1000' },
				{ label: '1000-2000', value: '1000-2000' },
				{ label: '2000-3000', value: '2000-3000' },
				{ label: '3000以上', value: '3000-99999' }
			],
			typeList: ['一居室', '二居室', '三居室', '合租', '整租']
		}
	},
	onLoad(options) {
		if (options.search) {
			// 从首页搜索进入
		}
		if (options.filter) {
			// 从首页筛选进入
		}
		this.loadHouseList();
	},
	onPullDownRefresh() {
		this.page = 1;
		this.houseList = [];
		this.hasMore = true;
		this.loadHouseList();
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	onReachBottom() {
		if (this.hasMore && !this.loading) {
			this.loadMore();
		}
	},
	methods: {
		// 加载房源列表
		async loadHouseList() {
			if (this.loading) return;
			
			this.loading = true;
			try {
				const res = await uniCloud.callFunction({
					name: 'getHouseList',
					data: {
						page: this.page,
						pageSize: this.pageSize,
						keyword: this.searchKeyword,
						area: this.selectedArea,
						price: this.selectedPrice,
						type: this.selectedType
					}
				});
				
				if (res.result.success) {
					const newList = res.result.data;
					if (this.page === 1) {
						this.houseList = newList;
					} else {
						this.houseList = [...this.houseList, ...newList];
					}
					
					this.hasMore = newList.length === this.pageSize;
				}
			} catch (error) {
				console.error('加载房源列表失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 搜索
		onSearch() {
			this.page = 1;
			this.houseList = [];
			this.hasMore = true;
			this.loadHouseList();
		},
		
		// 选择区域
		selectArea(area) {
			this.selectedArea = area;
			this.onSearch();
		},
		
		// 选择价格
		selectPrice(price) {
			this.selectedPrice = price;
			this.onSearch();
		},
		
		// 选择户型
		selectType(type) {
			this.selectedType = type;
			this.onSearch();
		},
		
		// 加载更多
		loadMore() {
			this.page++;
			this.loadHouseList();
		},
		
		// 切换收藏
		async toggleCollect(house, index) {
			try {
				const res = await uniCloud.callFunction({
					name: 'toggleCollect',
					data: {
						houseId: house._id
					}
				});
				
				if (res.result.success) {
					this.houseList[index].isCollected = !this.houseList[index].isCollected;
					uni.showToast({
						title: this.houseList[index].isCollected ? '收藏成功' : '取消收藏',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('收藏操作失败:', error);
			}
		},
		
		// 跳转到详情页
		goToDetail(houseId) {
			uni.navigateTo({
				url: `/pages/houseDetail/houseDetail?id=${houseId}`
			});
		},
		
		// 格式化时间
		formatTime(time) {
			const date = new Date(time);
			const now = new Date();
			const diff = now - date;
			const days = Math.floor(diff / (1000 * 60 * 60 * 24));
			
			if (days === 0) {
				return '今天';
			} else if (days === 1) {
				return '昨天';
			} else if (days < 7) {
				return `${days}天前`;
			} else {
				return `${date.getMonth() + 1}-${date.getDate()}`;
			}
		}
	}
}
</script>

<style scoped>
.house-list-page {
	background-color: #f8f8f8;
	min-height: 100vh;
}

/* 搜索栏样式 */
.search-section {
	padding: 20rpx;
	background-color: #fff;
}

.search-input {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #f5f5f5;
	border-radius: 50rpx;
}

.search-input input {
	flex: 1;
	font-size: 28rpx;
}

.search-btn {
	padding: 10rpx 20rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 30rpx;
	font-size: 26rpx;
}

/* 筛选栏样式 */
.filter-section {
	background-color: #fff;
	border-bottom: 1rpx solid #eee;
}

.filter-scroll {
	white-space: nowrap;
	padding: 20rpx;
}

.filter-row {
	display: flex;
	padding: 10rpx 20rpx;
	border-top: 1rpx solid #f5f5f5;
}

.filter-item {
	display: inline-block;
	padding: 15rpx 30rpx;
	margin-right: 20rpx;
	background-color: #f5f5f5;
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #666;
}

.filter-item.active {
	background-color: #3cc51f;
	color: #fff;
}

/* 房源列表样式 */
.house-list {
	padding: 20rpx;
}

.house-item {
	display: flex;
	padding: 30rpx;
	background-color: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	position: relative;
}

.house-image {
	width: 200rpx;
	height: 150rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
}

.house-info {
	flex: 1;
}

.house-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
}

.house-price {
	font-size: 36rpx;
	color: #ff6b35;
	font-weight: bold;
	margin-bottom: 15rpx;
}

.house-location {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.house-config {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.config-item {
	padding: 4rpx 12rpx;
	background-color: #e8f4fd;
	color: #1890ff;
	font-size: 22rpx;
	border-radius: 8rpx;
}

.house-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.publish-time {
	font-size: 24rpx;
	color: #999;
}

.house-type {
	padding: 4rpx 12rpx;
	background-color: #f0f0f0;
	color: #666;
	font-size: 22rpx;
	border-radius: 8rpx;
}

.house-actions {
	position: absolute;
	top: 30rpx;
	right: 30rpx;
}

.collect-btn {
	font-size: 48rpx;
}

.collect-btn.collected {
	color: #ff4757;
}

/* 加载更多样式 */
.load-more {
	padding: 40rpx;
	text-align: center;
}

.load-text {
	color: #3cc51f;
	font-size: 28rpx;
}

/* 空状态样式 */
.empty-state {
	padding: 200rpx 40rpx;
	text-align: center;
}

.empty-text {
	color: #999;
	font-size: 28rpx;
}
</style>
