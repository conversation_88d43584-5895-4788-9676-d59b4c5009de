'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	try {
		const userId = context.CLIENTIP; // 临时用IP作为用户标识
		
		// 获取用户发布数量
		const publishResult = await db.collection('house')
			.where({
				owner_id: userId
			})
			.count();
		
		// 获取用户收藏数量
		let collectCount = 0;
		try {
			const collectResult = await db.collection('user_collect')
				.where({
					user_id: userId
				})
				.count();
			collectCount = collectResult.total;
		} catch (error) {
			// 收藏表可能不存在
			console.log('收藏表不存在');
		}
		
		// 获取用户浏览记录数量
		let viewCount = 0;
		try {
			const viewResult = await db.collection('visit_logs')
				.where({
					user_id: userId
				})
				.count();
			viewCount = viewResult.total;
		} catch (error) {
			// 访问日志表可能不存在
			console.log('访问日志表不存在');
		}
		
		return {
			success: true,
			data: {
				publishCount: publishResult.total,
				collectCount: collectCount,
				viewCount: viewCount
			}
		};
		
	} catch (error) {
		console.error('获取用户统计失败:', error);
		return {
			success: false,
			message: '获取用户统计失败',
			error: error.message
		};
	}
};
