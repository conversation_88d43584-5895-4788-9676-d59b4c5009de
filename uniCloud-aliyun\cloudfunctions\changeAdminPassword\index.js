'use strict';

const db = uniCloud.database();
const crypto = require('crypto');

exports.main = async (event, context) => {
	const { oldPassword, newPassword } = event;
	
	if (!oldPassword || !newPassword) {
		return {
			success: false,
			message: '旧密码和新密码不能为空'
		};
	}
	
	if (newPassword.length < 6) {
		return {
			success: false,
			message: '新密码长度不能少于6位'
		};
	}
	
	try {
		// 获取当前管理员信息（这里简化处理，实际应该通过token获取）
		const adminResult = await db.collection('admin')
			.where({ username: 'admin' }) // 简化处理
			.get();
		
		if (adminResult.data.length === 0) {
			return {
				success: false,
				message: '管理员不存在'
			};
		}
		
		const admin = adminResult.data[0];
		
		// 验证旧密码
		const oldPasswordHash = generatePasswordHash(oldPassword);
		if (admin.password !== oldPasswordHash) {
			return {
				success: false,
				message: '当前密码错误'
			};
		}
		
		// 生成新密码哈希
		const newPasswordHash = generatePasswordHash(newPassword);
		
		// 更新密码
		await db.collection('admin')
			.doc(admin._id)
			.update({
				password: newPasswordHash,
				password_updated_at: new Date()
			});
		
		// 记录密码修改日志
		const passwordLog = {
			admin_id: admin._id,
			admin_username: admin.username,
			change_time: new Date(),
			ip: context.CLIENTIP
		};
		
		await db.collection('admin_password_logs').add(passwordLog);
		
		return {
			success: true,
			message: '密码修改成功'
		};
		
	} catch (error) {
		console.error('修改管理员密码失败:', error);
		return {
			success: false,
			message: '修改密码失败',
			error: error.message
		};
	}
};

// 生成密码哈希
function generatePasswordHash(password) {
	return crypto.createHash('md5').update(password + 'house_rental_salt').digest('hex');
}
