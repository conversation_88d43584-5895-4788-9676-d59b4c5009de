'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	try {
		const initResults = [];
		
		// 1. 初始化管理员账号
		try {
			const adminResult = await db.collection('admin')
				.where({ username: 'admin' })
				.get();
			
			if (adminResult.data.length === 0) {
				await db.collection('admin').add({
					username: 'admin',
					password: generatePasswordHash('123456'),
					role: 'super_admin',
					status: 'active',
					created_at: new Date(),
					last_login: null
				});
				initResults.push('✅ 创建默认管理员账号成功');
			} else {
				initResults.push('ℹ️ 管理员账号已存在');
			}
		} catch (error) {
			initResults.push(`❌ 初始化管理员账号失败: ${error.message}`);
		}
		
		// 2. 初始化系统设置
		try {
			const settingsResult = await db.collection('system_settings').get();
			
			if (settingsResult.data.length === 0) {
				await db.collection('system_settings').add({
					homeNotice: '欢迎使用房地产租赁平台！',
					noticeEnabled: true,
					autoAudit: false,
					allowRegister: true,
					dailyPublishLimit: 0,
					recommendAreas: '市中心,大学城,高新区,老城区,新区',
					hotTypes: '一居室,二居室,三居室,合租,整租',
					created_at: new Date(),
					updated_at: new Date()
				});
				initResults.push('✅ 创建默认系统设置成功');
			} else {
				initResults.push('ℹ️ 系统设置已存在');
			}
		} catch (error) {
			initResults.push(`❌ 初始化系统设置失败: ${error.message}`);
		}
		
		// 3. 创建示例房源数据
		try {
			const houseResult = await db.collection('house').count();
			
			if (houseResult.total === 0) {
				const sampleHouses = [
					{
						title: '精装修两居室，拎包入住',
						desc: '房屋位于市中心，交通便利，周边配套设施齐全，精装修，家具家电齐全。',
						images: ['/static/default-house.jpg'],
						location: {
							address: '市中心商业区',
							latitude: '39.9042',
							longitude: '116.4074'
						},
						price: 3500,
						type: '二居室',
						config: ['空调', '洗衣机', '热水器', '冰箱', '电视', 'WiFi'],
						contact: {
							phone: '13800138000',
							wechat: 'example_wechat'
						},
						owner_id: 'system',
						status: 'approved',
						view_count: 0,
						created_at: new Date(),
						updated_at: new Date()
					},
					{
						title: '温馨一居室，适合单身人士',
						desc: '房屋干净整洁，采光良好，适合单身人士或情侣居住。',
						images: ['/static/default-house.jpg'],
						location: {
							address: '大学城附近',
							latitude: '39.9042',
							longitude: '116.4074'
						},
						price: 2200,
						type: '一居室',
						config: ['空调', '热水器', '床', '衣柜', 'WiFi'],
						contact: {
							phone: '13800138001',
							wechat: 'example_wechat2'
						},
						owner_id: 'system',
						status: 'approved',
						view_count: 0,
						created_at: new Date(),
						updated_at: new Date()
					}
				];
				
				for (const house of sampleHouses) {
					await db.collection('house').add(house);
				}
				initResults.push('✅ 创建示例房源数据成功');
			} else {
				initResults.push('ℹ️ 房源数据已存在');
			}
		} catch (error) {
			initResults.push(`❌ 创建示例房源数据失败: ${error.message}`);
		}
		
		// 4. 创建数据库索引（如果支持的话）
		try {
			// 这里可以添加创建索引的代码
			// 由于uniCloud的限制，这里只是示例
			initResults.push('ℹ️ 数据库索引创建跳过（需要在控制台手动创建）');
		} catch (error) {
			initResults.push(`❌ 创建数据库索引失败: ${error.message}`);
		}
		
		return {
			success: true,
			message: '数据库初始化完成',
			data: {
				initTime: new Date(),
				results: initResults
			}
		};
		
	} catch (error) {
		console.error('数据库初始化失败:', error);
		return {
			success: false,
			message: '数据库初始化失败',
			error: error.message
		};
	}
};

// 生成密码哈希
function generatePasswordHash(password) {
	const crypto = require('crypto');
	return crypto.createHash('md5').update(password + 'house_rental_salt').digest('hex');
}
