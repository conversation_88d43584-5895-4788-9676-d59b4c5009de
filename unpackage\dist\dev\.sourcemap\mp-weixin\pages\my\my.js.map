{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端8/pages/my/my.vue?e863", "webpack:///D:/web/project/前端8/pages/my/my.vue?2502", "webpack:///D:/web/project/前端8/pages/my/my.vue?5c3b", "webpack:///D:/web/project/前端8/pages/my/my.vue?6812", "uni-app:///pages/my/my.vue", "webpack:///D:/web/project/前端8/pages/my/my.vue?089d", "webpack:///D:/web/project/前端8/pages/my/my.vue?3aac"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "nickname", "avatar", "phone", "userStats", "publishCount", "collectCount", "viewCount", "showAdminEntry", "onLoad", "onShow", "methods", "loadUserInfo", "console", "getUserProfile", "uni", "desc", "success", "fail", "loadUserStats", "uniCloud", "name", "res", "checkAdminAccess", "editProfile", "clickCount", "title", "icon", "goToMyPublish", "url", "goToMyCollect", "goToViewHistory", "goToSettings", "goToHelp", "goToAbout", "content", "showCancel", "goToAdmin"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,WAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACsD;AACL;AACqC;;;AAGtF;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,wEAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6nB,CAAgB,2nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkFjpB;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACAZ;kBACA;oBACA;kBACA;oBACA;oBACA;kBACA;gBACA;kBACAa;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACAC;QACAC;QACAC;UACA;UACAF;QACA;QACAG;UACAL;QACA;MACA;IACA;IAEA;IACAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAtB;gBACA;cAAA;gBAHAuB;gBAKA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAT;gBACA;gBACA;kBACAR;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgB;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACAC;MACAV;MAEA;QACA;QACAA;UACAW;UACAC;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACAb;QACAc;MACA;IACA;IAEA;IACAC;MACAf;QACAc;MACA;IACA;IAEA;IACAE;MACAhB;QACAW;QACAC;MACA;IACA;IAEA;IACAK;MACAjB;QACAW;QACAC;MACA;IACA;IAEA;IACAM;MACAlB;QACAW;QACAC;MACA;IACA;IAEA;IACAO;MACAnB;QACAW;QACAS;QACAC;MACA;IACA;IAEA;IACAC;MACAtB;QACAc;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjPA;AAAA;AAAA;AAAA;AAAo7B,CAAgB,84BAAG,EAAC,C;;;;;;;;;;;ACAx8B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/my.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/my/my.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my.vue?vue&type=template&id=0be17cc6&scoped=true&\"\nvar renderjs\nimport script from \"./my.vue?vue&type=script&lang=js&\"\nexport * from \"./my.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my.vue?vue&type=style&index=0&id=0be17cc6&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0be17cc6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/my.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=template&id=0be17cc6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"my-page\">\n\t\t<!-- 用户信息区域 -->\n\t\t<view class=\"user-section\">\n\t\t\t<view class=\"user-avatar\">\n\t\t\t\t<image :src=\"userInfo.avatar || '/static/default-avatar.png'\" mode=\"aspectFill\" class=\"avatar-image\" @click=\"editProfile\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"user-info\">\n\t\t\t\t<text class=\"user-name\">{{ userInfo.nickname || '微信用户' }}</text>\n\t\t\t\t<text class=\"user-phone\">{{ userInfo.phone || '未绑定手机' }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"user-actions\">\n\t\t\t\t<text class=\"edit-btn\" @click=\"editProfile\">编辑</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 统计信息 -->\n\t\t<view class=\"stats-section\">\n\t\t\t<view class=\"stat-item\" @click=\"goToMyPublish\">\n\t\t\t\t<text class=\"stat-number\">{{ userStats.publishCount }}</text>\n\t\t\t\t<text class=\"stat-label\">我的发布</text>\n\t\t\t</view>\n\t\t\t<view class=\"stat-item\" @click=\"goToMyCollect\">\n\t\t\t\t<text class=\"stat-number\">{{ userStats.collectCount }}</text>\n\t\t\t\t<text class=\"stat-label\">我的收藏</text>\n\t\t\t</view>\n\t\t\t<view class=\"stat-item\">\n\t\t\t\t<text class=\"stat-number\">{{ userStats.viewCount }}</text>\n\t\t\t\t<text class=\"stat-label\">浏览记录</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 功能菜单 -->\n\t\t<view class=\"menu-section\">\n\t\t\t<view class=\"menu-item\" @click=\"goToMyPublish\">\n\t\t\t\t<view class=\"menu-icon\">📤</view>\n\t\t\t\t<text class=\"menu-text\">我的发布</text>\n\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t</view>\n\t\t\t<view class=\"menu-item\" @click=\"goToMyCollect\">\n\t\t\t\t<view class=\"menu-icon\">❤️</view>\n\t\t\t\t<text class=\"menu-text\">我的收藏</text>\n\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t</view>\n\t\t\t<view class=\"menu-item\" @click=\"goToViewHistory\">\n\t\t\t\t<view class=\"menu-icon\">👁️</view>\n\t\t\t\t<text class=\"menu-text\">浏览记录</text>\n\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 设置菜单 -->\n\t\t<view class=\"menu-section\">\n\t\t\t<view class=\"menu-item\" @click=\"goToSettings\">\n\t\t\t\t<view class=\"menu-icon\">⚙️</view>\n\t\t\t\t<text class=\"menu-text\">设置</text>\n\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t</view>\n\t\t\t<view class=\"menu-item\" @click=\"goToHelp\">\n\t\t\t\t<view class=\"menu-icon\">❓</view>\n\t\t\t\t<text class=\"menu-text\">帮助与反馈</text>\n\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t</view>\n\t\t\t<view class=\"menu-item\" @click=\"goToAbout\">\n\t\t\t\t<view class=\"menu-icon\">ℹ️</view>\n\t\t\t\t<text class=\"menu-text\">关于我们</text>\n\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 管理员入口 -->\n\t\t<view class=\"admin-section\" v-if=\"showAdminEntry\">\n\t\t\t<view class=\"menu-item admin-entry\" @click=\"goToAdmin\">\n\t\t\t\t<view class=\"menu-icon\">👨‍💼</view>\n\t\t\t\t<text class=\"menu-text\">管理后台</text>\n\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tuserInfo: {\n\t\t\t\tnickname: '',\n\t\t\t\tavatar: '',\n\t\t\t\tphone: ''\n\t\t\t},\n\t\t\tuserStats: {\n\t\t\t\tpublishCount: 0,\n\t\t\t\tcollectCount: 0,\n\t\t\t\tviewCount: 0\n\t\t\t},\n\t\t\tshowAdminEntry: false\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.loadUserInfo();\n\t\tthis.loadUserStats();\n\t\tthis.checkAdminAccess();\n\t},\n\tonShow() {\n\t\t// 页面显示时刷新统计数据\n\t\tthis.loadUserStats();\n\t},\n\tmethods: {\n\t\t// 加载用户信息\n\t\tasync loadUserInfo() {\n\t\t\ttry {\n\t\t\t\t// 获取微信用户信息\n\t\t\t\tconst userInfo = uni.getStorageSync('userInfo');\n\t\t\t\tif (userInfo) {\n\t\t\t\t\tthis.userInfo = userInfo;\n\t\t\t\t} else {\n\t\t\t\t\t// 如果没有用户信息，尝试获取\n\t\t\t\t\tthis.getUserProfile();\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载用户信息失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取用户资料\n\t\tgetUserProfile() {\n\t\t\tuni.getUserProfile({\n\t\t\t\tdesc: '用于完善用户资料',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.userInfo = res.userInfo;\n\t\t\t\t\tuni.setStorageSync('userInfo', res.userInfo);\n\t\t\t\t},\n\t\t\t\tfail: (error) => {\n\t\t\t\t\tconsole.log('获取用户信息失败:', error);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 加载用户统计数据\n\t\tasync loadUserStats() {\n\t\t\ttry {\n\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\tname: 'getUserStats',\n\t\t\t\t\tdata: {}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.result.success) {\n\t\t\t\t\tthis.userStats = res.result.data;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载用户统计失败:', error);\n\t\t\t\t// 设置默认值\n\t\t\t\tthis.userStats = {\n\t\t\t\t\tpublishCount: 0,\n\t\t\t\t\tcollectCount: 0,\n\t\t\t\t\tviewCount: 0\n\t\t\t\t};\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 检查管理员权限\n\t\tcheckAdminAccess() {\n\t\t\t// 这里可以根据用户信息判断是否显示管理员入口\n\t\t\t// 简单示例：点击头像5次显示管理员入口\n\t\t\tlet clickCount = uni.getStorageSync('avatarClickCount') || 0;\n\t\t\tthis.showAdminEntry = clickCount >= 5;\n\t\t},\n\t\t\n\t\t// 编辑个人资料\n\t\teditProfile() {\n\t\t\t// 点击头像计数\n\t\t\tlet clickCount = uni.getStorageSync('avatarClickCount') || 0;\n\t\t\tclickCount++;\n\t\t\tuni.setStorageSync('avatarClickCount', clickCount);\n\t\t\t\n\t\t\tif (clickCount >= 5) {\n\t\t\t\tthis.showAdminEntry = true;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '管理员入口已开启',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 获取用户信息\n\t\t\tthis.getUserProfile();\n\t\t},\n\t\t\n\t\t// 跳转到我的发布\n\t\tgoToMyPublish() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/myPublish/myPublish'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到我的收藏\n\t\tgoToMyCollect() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/myCollect/myCollect'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到浏览记录\n\t\tgoToViewHistory() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '功能开发中',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到设置\n\t\tgoToSettings() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '功能开发中',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到帮助\n\t\tgoToHelp() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '功能开发中',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到关于我们\n\t\tgoToAbout() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '关于我们',\n\t\t\t\tcontent: '房地产租赁小程序 v1.0\\n为您提供便捷的房源租赁服务',\n\t\t\t\tshowCancel: false\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到管理后台\n\t\tgoToAdmin() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/admin/login'\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.my-page {\n\tbackground-color: #f8f9fa;\n\tmin-height: 100vh;\n}\n\n/* 用户信息区域 */\n.user-section {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 40rpx 30rpx;\n\tbackground-color: #fff;\n\tmargin-bottom: 20rpx;\n}\n\n.user-avatar {\n\tmargin-right: 30rpx;\n}\n\n.avatar-image {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 60rpx;\n}\n\n.user-info {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 10rpx;\n}\n\n.user-name {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.user-phone {\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n.user-actions {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.edit-btn {\n\tpadding: 15rpx 30rpx;\n\tbackground-color: #f5f5f5;\n\tcolor: #666;\n\tborder-radius: 25rpx;\n\tfont-size: 26rpx;\n}\n\n/* 统计信息 */\n.stats-section {\n\tdisplay: flex;\n\tbackground-color: #fff;\n\tmargin-bottom: 20rpx;\n}\n\n.stat-item {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 40rpx 20rpx;\n\tborder-right: 1rpx solid #f5f5f5;\n}\n\n.stat-item:last-child {\n\tborder-right: none;\n}\n\n.stat-number {\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\tcolor: #3cc51f;\n\tmargin-bottom: 10rpx;\n}\n\n.stat-label {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n/* 菜单区域 */\n.menu-section {\n\tbackground-color: #fff;\n\tmargin-bottom: 20rpx;\n}\n\n.menu-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #f5f5f5;\n}\n\n.menu-item:last-child {\n\tborder-bottom: none;\n}\n\n.menu-icon {\n\twidth: 60rpx;\n\tfont-size: 36rpx;\n\tmargin-right: 30rpx;\n}\n\n.menu-text {\n\tflex: 1;\n\tfont-size: 30rpx;\n\tcolor: #333;\n}\n\n.menu-arrow {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n\n/* 管理员入口 */\n.admin-section {\n\tbackground-color: #fff;\n}\n\n.admin-entry {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tcolor: #fff;\n\tborder-radius: 16rpx;\n\tmargin: 0 30rpx 30rpx;\n}\n\n.admin-entry .menu-text,\n.admin-entry .menu-arrow {\n\tcolor: #fff;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=style&index=0&id=0be17cc6&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=style&index=0&id=0be17cc6&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753953187026\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}