<template>
	<view class="my-publish">
		<!-- 状态筛选 -->
		<view class="status-tabs">
			<text class="status-tab" 
				  :class="{ active: selectedStatus === '' }" 
				  @click="selectStatus('')">全部</text>
			<text class="status-tab" 
				  :class="{ active: selectedStatus === 'pending' }" 
				  @click="selectStatus('pending')">待审核</text>
			<text class="status-tab" 
				  :class="{ active: selectedStatus === 'approved' }" 
				  @click="selectStatus('approved')">已通过</text>
			<text class="status-tab" 
				  :class="{ active: selectedStatus === 'rejected' }" 
				  @click="selectStatus('rejected')">已驳回</text>
		</view>
		
		<!-- 发布列表 -->
		<view class="publish-list" v-if="publishList.length > 0">
			<view class="publish-item" v-for="(house, index) in publishList" :key="house._id">
				<image :src="house.images[0]" mode="aspectFill" class="house-image" @click="goToDetail(house._id)"></image>
				<view class="house-info" @click="goToDetail(house._id)">
					<text class="house-title">{{ house.title }}</text>
					<text class="house-price">¥{{ house.price }}/月</text>
					<text class="house-location">{{ house.location.address }}</text>
					<view class="house-meta">
						<text class="publish-time">{{ formatTime(house.created_at) }}</text>
						<text class="view-count">浏览 {{ house.view_count || 0 }} 次</text>
					</view>
				</view>
				<view class="house-status">
					<text class="status-badge" :class="house.status">{{ getStatusText(house.status) }}</text>
					<view class="house-actions">
						<text class="action-btn edit" @click="editHouse(house._id)" v-if="house.status !== 'approved'">编辑</text>
						<text class="action-btn delete" @click="deleteHouse(house._id, index)">删除</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-else-if="!loading">
			<image src="/static/empty-publish.png" mode="aspectFit" class="empty-image"></image>
			<text class="empty-text">还没有发布过房源</text>
			<text class="empty-desc">发布您的第一个房源吧</text>
			<text class="go-publish-btn" @click="goToPublish">立即发布</text>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 驳回原因弹窗 -->
		<view class="modal-overlay" v-if="showRejectReason" @click="hideRejectReason">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">驳回原因</text>
					<text class="modal-close" @click="hideRejectReason">×</text>
				</view>
				<view class="modal-body">
					<text class="reject-reason">{{ currentRejectReason }}</text>
				</view>
				<view class="modal-footer">
					<text class="modal-btn" @click="hideRejectReason">知道了</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			selectedStatus: '',
			publishList: [],
			loading: true,
			showRejectReason: false,
			currentRejectReason: ''
		}
	},
	onLoad() {
		this.loadPublishList();
	},
	onShow() {
		// 页面显示时刷新列表
		this.loadPublishList();
	},
	onPullDownRefresh() {
		this.loadPublishList();
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	methods: {
		// 加载发布列表
		async loadPublishList() {
			this.loading = true;
			try {
				const res = await uniCloud.callFunction({
					name: 'getMyPublishList',
					data: {
						status: this.selectedStatus
					}
				});
				
				if (res.result.success) {
					this.publishList = res.result.data;
				} else {
					uni.showToast({
						title: res.result.message || '加载失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('加载发布列表失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 选择状态
		selectStatus(status) {
			this.selectedStatus = status;
			this.loadPublishList();
		},
		
		// 编辑房源
		editHouse(houseId) {
			uni.navigateTo({
				url: `/pages/editHouse/editHouse?id=${houseId}`
			});
		},
		
		// 删除房源
		deleteHouse(houseId, index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个房源吗？删除后无法恢复。',
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await uniCloud.callFunction({
								name: 'deleteMyHouse',
								data: { houseId: houseId }
							});
							
							if (result.result.success) {
								// 从列表中移除
								this.publishList.splice(index, 1);
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
							}
						} catch (error) {
							console.error('删除失败:', error);
							uni.showToast({
								title: '删除失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 查看驳回原因
		showRejectReasonModal(reason) {
			this.currentRejectReason = reason || '未提供具体原因';
			this.showRejectReason = true;
		},
		
		// 隐藏驳回原因弹窗
		hideRejectReason() {
			this.showRejectReason = false;
			this.currentRejectReason = '';
		},
		
		// 跳转到房源详情
		goToDetail(houseId) {
			uni.navigateTo({
				url: `/pages/houseDetail/houseDetail?id=${houseId}`
			});
		},
		
		// 跳转到发布页面
		goToPublish() {
			uni.switchTab({
				url: '/pages/postHouse/postHouse'
			});
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'pending': '待审核',
				'approved': '已通过',
				'rejected': '已驳回'
			};
			return statusMap[status] || '未知';
		},
		
		// 格式化时间
		formatTime(time) {
			const date = new Date(time);
			const now = new Date();
			const diff = now - date;
			const days = Math.floor(diff / (1000 * 60 * 60 * 24));
			
			if (days === 0) {
				return '今天';
			} else if (days === 1) {
				return '昨天';
			} else if (days < 30) {
				return `${days}天前`;
			} else {
				return `${date.getMonth() + 1}-${date.getDate()}`;
			}
		}
	}
}
</script>

<style scoped>
.my-publish {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 状态筛选 */
.status-tabs {
	display: flex;
	background-color: #fff;
	padding: 30rpx;
	gap: 30rpx;
	border-bottom: 1rpx solid #eee;
}

.status-tab {
	padding: 15rpx 25rpx;
	background-color: #f5f5f5;
	border-radius: 25rpx;
	font-size: 26rpx;
	color: #666;
}

.status-tab.active {
	background-color: #3cc51f;
	color: #fff;
}

/* 发布列表 */
.publish-list {
	padding: 30rpx;
}

.publish-item {
	display: flex;
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.house-image {
	width: 200rpx;
	height: 150rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
}

.house-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.house-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.house-price {
	font-size: 32rpx;
	color: #ff6b35;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.house-location {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.house-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.publish-time {
	font-size: 22rpx;
	color: #999;
}

.view-count {
	font-size: 22rpx;
	color: #999;
}

.house-status {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 15rpx;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.status-badge.pending {
	background-color: #fff7e6;
	color: #fa8c16;
}

.status-badge.approved {
	background-color: #f6ffed;
	color: #52c41a;
}

.status-badge.rejected {
	background-color: #fff2f0;
	color: #ff4d4f;
}

.house-actions {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.action-btn {
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	text-align: center;
	color: #fff;
}

.action-btn.edit {
	background-color: #1890ff;
}

.action-btn.delete {
	background-color: #ff4757;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 200rpx 40rpx;
	text-align: center;
}

.empty-image {
	width: 300rpx;
	height: 300rpx;
	margin-bottom: 40rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 20rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #999;
	margin-bottom: 40rpx;
}

.go-publish-btn {
	padding: 25rpx 50rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 30rpx;
	font-size: 28rpx;
}

/* 加载状态 */
.loading-state {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 200rpx 40rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	width: 600rpx;
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 48rpx;
	color: #999;
}

.modal-body {
	padding: 30rpx;
}

.reject-reason {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.modal-footer {
	padding: 30rpx;
	border-top: 1rpx solid #eee;
}

.modal-btn {
	width: 100%;
	padding: 25rpx;
	text-align: center;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 12rpx;
	font-size: 28rpx;
}
</style>
