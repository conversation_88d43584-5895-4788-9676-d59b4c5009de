'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const {
		homeNotice,
		noticeEnabled,
		autoAudit,
		allowRegister,
		dailyPublishLimit,
		recommendAreas,
		hotTypes
	} = event;
	
	try {
		// 验证必要参数
		if (typeof noticeEnabled !== 'boolean' || 
			typeof autoAudit !== 'boolean' || 
			typeof allowRegister !== 'boolean') {
			return {
				success: false,
				message: '参数格式错误'
			};
		}
		
		// 构建设置数据
		const settingsData = {
			homeNotice: homeNotice || '',
			noticeEnabled: noticeEnabled,
			autoAudit: autoAudit,
			allowRegister: allowRegister,
			dailyPublishLimit: Number(dailyPublishLimit) || 0,
			recommendAreas: recommendAreas || '',
			hotTypes: hotTypes || '',
			updated_at: new Date()
		};
		
		// 检查是否已有设置记录
		const existingResult = await db.collection('system_settings')
			.orderBy('updated_at', 'desc')
			.limit(1)
			.get();
		
		if (existingResult.data.length > 0) {
			// 更新现有设置
			await db.collection('system_settings')
				.doc(existingResult.data[0]._id)
				.update(settingsData);
		} else {
			// 创建新的设置记录
			settingsData.created_at = new Date();
			await db.collection('system_settings').add(settingsData);
		}
		
		// 记录设置变更日志
		const changeLog = {
			settings: settingsData,
			change_time: new Date(),
			admin_ip: context.CLIENTIP
		};
		
		await db.collection('settings_change_logs').add(changeLog);
		
		return {
			success: true,
			message: '设置保存成功'
		};
		
	} catch (error) {
		console.error('保存系统设置失败:', error);
		return {
			success: false,
			message: '保存系统设置失败',
			error: error.message
		};
	}
};
