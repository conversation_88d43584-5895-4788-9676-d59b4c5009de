{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端8/pages/houseDetail/houseDetail.vue?0d7b", "webpack:///D:/web/project/前端8/pages/houseDetail/houseDetail.vue?89f1", "webpack:///D:/web/project/前端8/pages/houseDetail/houseDetail.vue?3d56", "webpack:///D:/web/project/前端8/pages/houseDetail/houseDetail.vue?e2e1", "uni-app:///pages/houseDetail/houseDetail.vue", "webpack:///D:/web/project/前端8/pages/houseDetail/houseDetail.vue?43ef", "webpack:///D:/web/project/前端8/pages/houseDetail/houseDetail.vue?5d02"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "houseId", "houseInfo", "onLoad", "methods", "loadHouseDetail", "uniCloud", "name", "res", "uni", "title", "icon", "setTimeout", "console", "recordVisit", "page", "previewImage", "urls", "current", "toggleCollect", "makeCall", "phoneNumber", "copyWechat", "success", "reportHouse", "itemList", "reasons", "reason", "contactOwner", "goBack", "getStatusText", "getConfigIcon"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAsoB,CAAgB,ooBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0F1pB;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAP;oBACAC;kBACA;gBACA;cAAA;gBALAO;gBAOA;kBACA;kBACA;kBACAC;oBACAC;kBACA;gBACA;kBACAD;oBACAC;oBACAC;kBACA;kBACAC;oBACAH;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAI;gBACAJ;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAR;kBACAC;kBACAP;oBACAC;oBACAc;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MACAP;QACAQ;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAb;kBACAC;kBACAP;oBACAC;kBACA;gBACA;cAAA;gBALAO;gBAOA;kBACA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAE;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MACAX;QACAY;MACA;IACA;IAEA;IACAC;MACAb;QACAT;QACAuB;UACAd;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAa;MAAA;MACAf;QACAgB;QACAF;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBACAG;oBAAA;oBAAA;oBAAA,OAEApB;sBACAC;sBACAP;wBACAC;wBACA0B;sBACA;oBACA;kBAAA;oBACAlB;sBACAC;sBACAC;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEAE;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAe;MACA;QACA;MACA;QACA;MACA;QACAnB;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAkB;MACApB;IACA;IAEA;IACAqB;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjRA;AAAA;AAAA;AAAA;AAA67B,CAAgB,u5BAAG,EAAC,C;;;;;;;;;;;ACAj9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/houseDetail/houseDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/houseDetail/houseDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./houseDetail.vue?vue&type=template&id=6992ea2a&scoped=true&\"\nvar renderjs\nimport script from \"./houseDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./houseDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./houseDetail.vue?vue&type=style&index=0&id=6992ea2a&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6992ea2a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/houseDetail/houseDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseDetail.vue?vue&type=template&id=6992ea2a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.houseInfo ? _vm.getStatusText(_vm.houseInfo.status) : null\n  var l0 = _vm.houseInfo\n    ? _vm.__map(_vm.houseInfo.config, function (config, index) {\n        var $orig = _vm.__get_orig(config)\n        var m1 = _vm.getConfigIcon(config)\n        return {\n          $orig: $orig,\n          m1: m1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseDetail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"house-detail\" v-if=\"houseInfo\">\n\t\t<!-- 图片轮播 -->\n\t\t<swiper class=\"image-swiper\" indicator-dots=\"true\" autoplay=\"false\">\n\t\t\t<swiper-item v-for=\"(image, index) in houseInfo.images\" :key=\"index\">\n\t\t\t\t<image :src=\"image\" mode=\"aspectFill\" class=\"detail-image\" @click=\"previewImage(index)\"></image>\n\t\t\t</swiper-item>\n\t\t</swiper>\n\t\t\n\t\t<!-- 基本信息 -->\n\t\t<view class=\"basic-info\">\n\t\t\t<view class=\"price-section\">\n\t\t\t\t<text class=\"price\">¥{{ houseInfo.price }}</text>\n\t\t\t\t<text class=\"price-unit\">/月</text>\n\t\t\t\t<view class=\"collect-btn\" @click=\"toggleCollect\">\n\t\t\t\t\t<text class=\"collect-icon\">{{ houseInfo.isCollected ? '❤️' : '🤍' }}</text>\n\t\t\t\t\t<text class=\"collect-text\">{{ houseInfo.isCollected ? '已收藏' : '收藏' }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"title\">{{ houseInfo.title }}</view>\n\t\t\t\n\t\t\t<view class=\"location\">\n\t\t\t\t<text class=\"location-icon\">📍</text>\n\t\t\t\t<text class=\"location-text\">{{ houseInfo.location.address }}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"house-tags\">\n\t\t\t\t<text class=\"tag type-tag\">{{ houseInfo.type }}</text>\n\t\t\t\t<text class=\"tag status-tag\" :class=\"houseInfo.status\">{{ getStatusText(houseInfo.status) }}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 房源配置 -->\n\t\t<view class=\"config-section\">\n\t\t\t<view class=\"section-title\">房源配置</view>\n\t\t\t<view class=\"config-grid\">\n\t\t\t\t<view class=\"config-item\" v-for=\"(config, index) in houseInfo.config\" :key=\"index\">\n\t\t\t\t\t<text class=\"config-icon\">{{ getConfigIcon(config) }}</text>\n\t\t\t\t\t<text class=\"config-text\">{{ config }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 详细描述 -->\n\t\t<view class=\"description-section\">\n\t\t\t<view class=\"section-title\">房源描述</view>\n\t\t\t<text class=\"description-text\">{{ houseInfo.desc }}</text>\n\t\t</view>\n\t\t\n\t\t<!-- 联系信息 -->\n\t\t<view class=\"contact-section\">\n\t\t\t<view class=\"section-title\">联系房东</view>\n\t\t\t<view class=\"contact-info\">\n\t\t\t\t<view class=\"contact-item\" v-if=\"houseInfo.contact.phone\">\n\t\t\t\t\t<text class=\"contact-label\">电话：</text>\n\t\t\t\t\t<text class=\"contact-value\">{{ houseInfo.contact.phone }}</text>\n\t\t\t\t\t<text class=\"contact-btn\" @click=\"makeCall\">拨打</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"contact-item\" v-if=\"houseInfo.contact.wechat\">\n\t\t\t\t\t<text class=\"contact-label\">微信：</text>\n\t\t\t\t\t<text class=\"contact-value\">{{ houseInfo.contact.wechat }}</text>\n\t\t\t\t\t<text class=\"contact-btn\" @click=\"copyWechat\">复制</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 举报按钮 -->\n\t\t<view class=\"report-section\">\n\t\t\t<text class=\"report-btn\" @click=\"reportHouse\">举报房源</text>\n\t\t</view>\n\t\t\n\t\t<!-- 底部操作栏 -->\n\t\t<view class=\"bottom-actions\">\n\t\t\t<view class=\"action-btn secondary\" @click=\"goBack\">\n\t\t\t\t<text>返回</text>\n\t\t\t</view>\n\t\t\t<view class=\"action-btn primary\" @click=\"contactOwner\">\n\t\t\t\t<text>立即联系</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\t\n\t<!-- 加载状态 -->\n\t<view class=\"loading\" v-else>\n\t\t<text>加载中...</text>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\thouseId: '',\n\t\t\thouseInfo: null\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tthis.houseId = options.id;\n\t\tthis.loadHouseDetail();\n\t\tthis.recordVisit();\n\t},\n\tmethods: {\n\t\t// 加载房源详情\n\t\tasync loadHouseDetail() {\n\t\t\ttry {\n\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\tname: 'getHouseDetail',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\thouseId: this.houseId\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.result.success) {\n\t\t\t\t\tthis.houseInfo = res.result.data;\n\t\t\t\t\t// 设置页面标题\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: this.houseInfo.title\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '房源不存在',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 1500);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载房源详情失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 记录访问\n\t\tasync recordVisit() {\n\t\t\ttry {\n\t\t\t\tawait uniCloud.callFunction({\n\t\t\t\t\tname: 'recordVisit',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\thouseId: this.houseId,\n\t\t\t\t\t\tpage: 'detail'\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('记录访问失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 预览图片\n\t\tpreviewImage(index) {\n\t\t\tuni.previewImage({\n\t\t\t\turls: this.houseInfo.images,\n\t\t\t\tcurrent: index\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 切换收藏\n\t\tasync toggleCollect() {\n\t\t\ttry {\n\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\tname: 'toggleCollect',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\thouseId: this.houseId\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.result.success) {\n\t\t\t\t\tthis.houseInfo.isCollected = !this.houseInfo.isCollected;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: this.houseInfo.isCollected ? '收藏成功' : '取消收藏',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('收藏操作失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 拨打电话\n\t\tmakeCall() {\n\t\t\tuni.makePhoneCall({\n\t\t\t\tphoneNumber: this.houseInfo.contact.phone\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 复制微信号\n\t\tcopyWechat() {\n\t\t\tuni.setClipboardData({\n\t\t\t\tdata: this.houseInfo.contact.wechat,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '微信号已复制',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 举报房源\n\t\treportHouse() {\n\t\t\tuni.showActionSheet({\n\t\t\t\titemList: ['虚假信息', '重复发布', '价格不符', '其他问题'],\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tconst reasons = ['虚假信息', '重复发布', '价格不符', '其他问题'];\n\t\t\t\t\ttry {\n\t\t\t\t\t\tawait uniCloud.callFunction({\n\t\t\t\t\t\t\tname: 'reportHouse',\n\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\thouseId: this.houseId,\n\t\t\t\t\t\t\t\treason: reasons[res.tapIndex]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '举报成功',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('举报失败:', error);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 联系房东\n\t\tcontactOwner() {\n\t\t\tif (this.houseInfo.contact.phone) {\n\t\t\t\tthis.makeCall();\n\t\t\t} else if (this.houseInfo.contact.wechat) {\n\t\t\t\tthis.copyWechat();\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '暂无联系方式',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 返回\n\t\tgoBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\t\n\t\t// 获取状态文本\n\t\tgetStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t'pending': '待审核',\n\t\t\t\t'approved': '已发布',\n\t\t\t\t'rejected': '已驳回'\n\t\t\t};\n\t\t\treturn statusMap[status] || '未知';\n\t\t},\n\t\t\n\t\t// 获取配置图标\n\t\tgetConfigIcon(config) {\n\t\t\tconst iconMap = {\n\t\t\t\t'空调': '❄️',\n\t\t\t\t'洗衣机': '🧺',\n\t\t\t\t'热水器': '🚿',\n\t\t\t\t'冰箱': '🧊',\n\t\t\t\t'电视': '📺',\n\t\t\t\t'WiFi': '📶',\n\t\t\t\t'床': '🛏️',\n\t\t\t\t'衣柜': '👔',\n\t\t\t\t'桌椅': '🪑',\n\t\t\t\t'阳台': '🌅'\n\t\t\t};\n\t\t\treturn iconMap[config] || '✅';\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.house-detail {\n\tbackground-color: #f8f8f8;\n\tmin-height: 100vh;\n\tpadding-bottom: 120rpx;\n}\n\n/* 图片轮播样式 */\n.image-swiper {\n\twidth: 100%;\n\theight: 500rpx;\n}\n\n.detail-image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n/* 基本信息样式 */\n.basic-info {\n\tbackground-color: #fff;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.price-section {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-bottom: 20rpx;\n}\n\n.price {\n\tfont-size: 48rpx;\n\tcolor: #ff6b35;\n\tfont-weight: bold;\n}\n\n.price-unit {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-left: 10rpx;\n}\n\n.collect-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 15rpx 25rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 30rpx;\n}\n\n.collect-icon {\n\tfont-size: 32rpx;\n\tmargin-right: 10rpx;\n}\n\n.collect-text {\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n.title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n}\n\n.location {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.location-icon {\n\tfont-size: 28rpx;\n\tmargin-right: 10rpx;\n}\n\n.location-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.house-tags {\n\tdisplay: flex;\n\tgap: 15rpx;\n}\n\n.tag {\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n}\n\n.type-tag {\n\tbackground-color: #e8f4fd;\n\tcolor: #1890ff;\n}\n\n.status-tag.approved {\n\tbackground-color: #f6ffed;\n\tcolor: #52c41a;\n}\n\n.status-tag.pending {\n\tbackground-color: #fff7e6;\n\tcolor: #fa8c16;\n}\n\n/* 配置区域样式 */\n.config-section {\n\tbackground-color: #fff;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 30rpx;\n}\n\n.config-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 20rpx;\n}\n\n.config-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 15rpx 20rpx;\n\tbackground-color: #f9f9f9;\n\tborder-radius: 12rpx;\n\tmin-width: 200rpx;\n}\n\n.config-icon {\n\tfont-size: 32rpx;\n\tmargin-right: 15rpx;\n}\n\n.config-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n/* 描述区域样式 */\n.description-section {\n\tbackground-color: #fff;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.description-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n}\n\n/* 联系信息样式 */\n.contact-section {\n\tbackground-color: #fff;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.contact-info {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.contact-item {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx;\n\tbackground-color: #f9f9f9;\n\tborder-radius: 12rpx;\n}\n\n.contact-label {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.contact-value {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-left: 20rpx;\n}\n\n.contact-btn {\n\tpadding: 10rpx 20rpx;\n\tbackground-color: #3cc51f;\n\tcolor: #fff;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n}\n\n/* 举报区域样式 */\n.report-section {\n\tpadding: 30rpx;\n\ttext-align: center;\n}\n\n.report-btn {\n\tcolor: #999;\n\tfont-size: 26rpx;\n\ttext-decoration: underline;\n}\n\n/* 底部操作栏样式 */\n.bottom-actions {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tdisplay: flex;\n\tpadding: 20rpx;\n\tbackground-color: #fff;\n\tborder-top: 1rpx solid #eee;\n\tgap: 20rpx;\n}\n\n.action-btn {\n\tflex: 1;\n\tpadding: 25rpx;\n\ttext-align: center;\n\tborder-radius: 12rpx;\n\tfont-size: 32rpx;\n}\n\n.action-btn.secondary {\n\tbackground-color: #f5f5f5;\n\tcolor: #666;\n}\n\n.action-btn.primary {\n\tbackground-color: #3cc51f;\n\tcolor: #fff;\n}\n\n/* 加载状态样式 */\n.loading {\n\tpadding: 200rpx 40rpx;\n\ttext-align: center;\n\tcolor: #999;\n\tfont-size: 28rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseDetail.vue?vue&type=style&index=0&id=6992ea2a&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseDetail.vue?vue&type=style&index=0&id=6992ea2a&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753953187018\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}