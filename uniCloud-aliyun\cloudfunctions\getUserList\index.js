'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const {
		page = 1,
		pageSize = 10,
		keyword = '',
		status = ''
	} = event;
	
	try {
		// 构建查询条件
		let whereCondition = {};
		
		// 关键词搜索
		if (keyword) {
			whereCondition.$or = [
				{ nickname: new RegExp(keyword, 'i') },
				{ phone: new RegExp(keyword, 'i') }
			];
		}
		
		// 状态筛选
		if (status) {
			whereCondition.status = status;
		}
		
		// 查询用户列表
		let query = db.collection('user').where(whereCondition);
		
		// 按注册时间倒序
		query = query.orderBy('created_at', 'desc');
		
		// 分页
		const skip = (page - 1) * pageSize;
		query = query.skip(skip).limit(pageSize);
		
		// 执行查询
		const result = await query.get();
		
		// 为每个用户添加统计信息
		const userList = await Promise.all(result.data.map(async (user) => {
			// 获取用户发布数量
			const publishResult = await db.collection('house')
				.where({ owner_id: user._id })
				.count();
			
			// 获取用户收藏数量
			let collectCount = 0;
			try {
				const collectResult = await db.collection('user_collect')
					.where({ user_id: user._id })
					.count();
				collectCount = collectResult.total;
			} catch (error) {
				// 收藏表可能不存在
			}
			
			return {
				...user,
				publishCount: publishResult.total,
				collectCount: collectCount,
				status: user.status || 'active' // 默认状态为正常
			};
		}));
		
		return {
			success: true,
			data: userList,
			total: result.data.length,
			page: page,
			pageSize: pageSize
		};
		
	} catch (error) {
		console.error('获取用户列表失败:', error);
		return {
			success: false,
			message: '获取用户列表失败',
			error: error.message
		};
	}
};
