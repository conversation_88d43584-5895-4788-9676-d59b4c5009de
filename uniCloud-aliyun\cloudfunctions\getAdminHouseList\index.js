'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const {
		page = 1,
		pageSize = 10,
		keyword = '',
		status = ''
	} = event;
	
	try {
		// 构建查询条件
		let whereCondition = {};
		
		// 关键词搜索
		if (keyword) {
			whereCondition.$or = [
				{ title: new RegExp(keyword, 'i') },
				{ desc: new RegExp(keyword, 'i') },
				{ 'location.address': new RegExp(keyword, 'i') }
			];
		}
		
		// 状态筛选
		if (status) {
			whereCondition.status = status;
		}
		
		// 查询房源列表
		let query = db.collection('house').where(whereCondition);
		
		// 按创建时间倒序
		query = query.orderBy('created_at', 'desc');
		
		// 分页
		const skip = (page - 1) * pageSize;
		query = query.skip(skip).limit(pageSize);
		
		// 执行查询
		const result = await query.get();
		
		// 处理返回数据
		const houseList = result.data.map(house => {
			return {
				...house,
				images: house.images || ['/static/default-house.jpg']
			};
		});
		
		return {
			success: true,
			data: houseList,
			total: result.data.length,
			page: page,
			pageSize: pageSize
		};
		
	} catch (error) {
		console.error('获取管理员房源列表失败:', error);
		return {
			success: false,
			message: '获取房源列表失败',
			error: error.message
		};
	}
};
