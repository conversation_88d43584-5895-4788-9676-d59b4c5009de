'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const { status = '' } = event;
	
	try {
		const userId = context.CLIENTIP; // 临时用IP作为用户标识
		
		// 构建查询条件
		let whereCondition = {
			owner_id: userId
		};
		
		// 状态筛选
		if (status) {
			whereCondition.status = status;
		}
		
		// 查询用户发布的房源
		const result = await db.collection('house')
			.where(whereCondition)
			.orderBy('created_at', 'desc')
			.get();
		
		return {
			success: true,
			data: result.data
		};
		
	} catch (error) {
		console.error('获取我的发布列表失败:', error);
		return {
			success: false,
			message: '获取发布列表失败',
			error: error.message
		};
	}
};
