
.my-publish.data-v-0c16060e {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 状态筛选 */
.status-tabs.data-v-0c16060e {
	display: flex;
	background-color: #fff;
	padding: 30rpx;
	gap: 30rpx;
	border-bottom: 1rpx solid #eee;
}
.status-tab.data-v-0c16060e {
	padding: 15rpx 25rpx;
	background-color: #f5f5f5;
	border-radius: 25rpx;
	font-size: 26rpx;
	color: #666;
}
.status-tab.active.data-v-0c16060e {
	background-color: #3cc51f;
	color: #fff;
}

/* 发布列表 */
.publish-list.data-v-0c16060e {
	padding: 30rpx;
}
.publish-item.data-v-0c16060e {
	display: flex;
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.house-image.data-v-0c16060e {
	width: 200rpx;
	height: 150rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
}
.house-info.data-v-0c16060e {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.house-title.data-v-0c16060e {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}
.house-price.data-v-0c16060e {
	font-size: 32rpx;
	color: #ff6b35;
	font-weight: bold;
	margin-bottom: 10rpx;
}
.house-location.data-v-0c16060e {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 10rpx;
}
.house-meta.data-v-0c16060e {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.publish-time.data-v-0c16060e {
	font-size: 22rpx;
	color: #999;
}
.view-count.data-v-0c16060e {
	font-size: 22rpx;
	color: #999;
}
.house-status.data-v-0c16060e {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 15rpx;
}
.status-badge.data-v-0c16060e {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}
.status-badge.pending.data-v-0c16060e {
	background-color: #fff7e6;
	color: #fa8c16;
}
.status-badge.approved.data-v-0c16060e {
	background-color: #f6ffed;
	color: #52c41a;
}
.status-badge.rejected.data-v-0c16060e {
	background-color: #fff2f0;
	color: #ff4d4f;
}
.house-actions.data-v-0c16060e {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}
.action-btn.data-v-0c16060e {
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	text-align: center;
	color: #fff;
}
.action-btn.edit.data-v-0c16060e {
	background-color: #1890ff;
}
.action-btn.delete.data-v-0c16060e {
	background-color: #ff4757;
}

/* 空状态 */
.empty-state.data-v-0c16060e {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 200rpx 40rpx;
	text-align: center;
}
.empty-image.data-v-0c16060e {
	width: 300rpx;
	height: 300rpx;
	margin-bottom: 40rpx;
	opacity: 0.6;
}
.empty-text.data-v-0c16060e {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 20rpx;
}
.empty-desc.data-v-0c16060e {
	font-size: 26rpx;
	color: #999;
	margin-bottom: 40rpx;
}
.go-publish-btn.data-v-0c16060e {
	padding: 25rpx 50rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 30rpx;
	font-size: 28rpx;
}

/* 加载状态 */
.loading-state.data-v-0c16060e {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 200rpx 40rpx;
}
.loading-text.data-v-0c16060e {
	font-size: 28rpx;
	color: #999;
}

/* 弹窗样式 */
.modal-overlay.data-v-0c16060e {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}
.modal-content.data-v-0c16060e {
	width: 600rpx;
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}
.modal-header.data-v-0c16060e {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
}
.modal-title.data-v-0c16060e {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.modal-close.data-v-0c16060e {
	font-size: 48rpx;
	color: #999;
}
.modal-body.data-v-0c16060e {
	padding: 30rpx;
}
.reject-reason.data-v-0c16060e {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}
.modal-footer.data-v-0c16060e {
	padding: 30rpx;
	border-top: 1rpx solid #eee;
}
.modal-btn.data-v-0c16060e {
	width: 100%;
	padding: 25rpx;
	text-align: center;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 12rpx;
	font-size: 28rpx;
}

