<view class="post-house data-v-28698208"><form data-event-opts="{{[['submit',[['submitForm',['$event']]]]]}}" bindsubmit="__e" class="data-v-28698208"><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">房源标题</view><input class="form-input data-v-28698208" placeholder="请输入房源标题" maxlength="50" data-event-opts="{{[['input',[['__set_model',['$0','title','$event',[]],['formData']]]]]}}" value="{{formData.title}}" bindinput="__e"/></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">房源图片 (最多9张)</view><view class="image-upload data-v-28698208"><block wx:for="{{formData.images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view class="image-item data-v-28698208"><image class="uploaded-image data-v-28698208" src="{{image}}" mode="aspectFill"></image><text data-event-opts="{{[['tap',[['deleteImage',[index]]]]]}}" class="delete-btn data-v-28698208" bindtap="__e">×</text></view></block><block wx:if="{{$root.g0<9}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="upload-btn data-v-28698208" bindtap="__e"><text class="upload-icon data-v-28698208">+</text><text class="upload-text data-v-28698208">添加图片</text></view></block></view></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">基本信息</view><view class="form-row data-v-28698208"><text class="form-label data-v-28698208">租金</text><input class="form-input data-v-28698208" type="number" placeholder="请输入月租金" data-event-opts="{{[['input',[['__set_model',['$0','price','$event',[]],['formData']]]]]}}" value="{{formData.price}}" bindinput="__e"/><text class="form-unit data-v-28698208">元/月</text></view><view class="form-row data-v-28698208"><text class="form-label data-v-28698208">户型</text><picker class="form-picker data-v-28698208" value="{{typeIndex}}" range="{{typeList}}" data-event-opts="{{[['change',[['onTypeChange',['$event']]]]]}}" bindchange="__e"><view class="picker-text data-v-28698208">{{typeList[typeIndex]||'请选择户型'}}</view></picker></view></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">位置信息</view><view data-event-opts="{{[['tap',[['chooseLocation',['$event']]]]]}}" class="location-input data-v-28698208" bindtap="__e"><text class="location-text data-v-28698208">{{formData.location.address||'点击选择位置'}}</text><text class="location-icon data-v-28698208">📍</text></view></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">房源配置</view><view class="config-grid data-v-28698208"><block wx:for="{{$root.l0}}" wx:for-item="config" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toggleConfig',['$0'],[[['configList','',index]]]]]]]}}" class="{{['config-item','data-v-28698208',(config.g1)?'active':'']}}" bindtap="__e"><text class="config-icon data-v-28698208">{{config.m0}}</text><text class="config-text data-v-28698208">{{config.$orig}}</text></view></block></view></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">联系方式</view><view class="form-row data-v-28698208"><text class="form-label data-v-28698208">电话</text><input class="form-input data-v-28698208" placeholder="请输入联系电话" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['formData.contact']]]]]}}" value="{{formData.contact.phone}}" bindinput="__e"/></view><view class="form-row data-v-28698208"><text class="form-label data-v-28698208">微信</text><input class="form-input data-v-28698208" placeholder="请输入微信号" data-event-opts="{{[['input',[['__set_model',['$0','wechat','$event',[]],['formData.contact']]]]]}}" value="{{formData.contact.wechat}}" bindinput="__e"/></view></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">详细描述</view><textarea class="form-textarea data-v-28698208" placeholder="请详细描述房源情况，如周边环境、交通便利性等" maxlength="500" data-event-opts="{{[['input',[['__set_model',['$0','desc','$event',[]],['formData']]]]]}}" value="{{formData.desc}}" bindinput="__e"></textarea></view><view class="submit-section data-v-28698208"><button class="submit-btn data-v-28698208" disabled="{{submitting}}" data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" bindtap="__e">{{''+(submitting?'发布中...':'发布房源')+''}}</button></view></form></view>