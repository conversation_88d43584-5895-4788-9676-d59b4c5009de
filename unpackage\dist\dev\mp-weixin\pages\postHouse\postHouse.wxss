
.post-house.data-v-28698208 {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

/* 表单区域样式 */
.form-section.data-v-28698208 {
	background-color: #fff;
	margin-bottom: 20rpx;
	padding: 30rpx;
}
.section-title.data-v-28698208 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}
.form-input.data-v-28698208 {
	width: 100%;
	padding: 25rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
	font-size: 28rpx;
	margin-bottom: 20rpx;
}
.form-row.data-v-28698208 {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.form-label.data-v-28698208 {
	width: 120rpx;
	font-size: 28rpx;
	color: #333;
}
.form-unit.data-v-28698208 {
	margin-left: 20rpx;
	font-size: 28rpx;
	color: #666;
}
.form-picker.data-v-28698208 {
	flex: 1;
	padding: 25rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
}
.picker-text.data-v-28698208 {
	font-size: 28rpx;
	color: #333;
}
.form-textarea.data-v-28698208 {
	width: 100%;
	min-height: 200rpx;
	padding: 25rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
	font-size: 28rpx;
}

/* 图片上传样式 */
.image-upload.data-v-28698208 {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}
.image-item.data-v-28698208 {
	position: relative;
	width: 200rpx;
	height: 200rpx;
}
.uploaded-image.data-v-28698208 {
	width: 100%;
	height: 100%;
	border-radius: 12rpx;
}
.delete-btn.data-v-28698208 {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	background-color: #ff4757;
	color: #fff;
	border-radius: 50%;
	text-align: center;
	line-height: 40rpx;
	font-size: 24rpx;
}
.upload-btn.data-v-28698208 {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 200rpx;
	height: 200rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
	border: 2rpx dashed #ccc;
}
.upload-icon.data-v-28698208 {
	font-size: 48rpx;
	color: #999;
	margin-bottom: 10rpx;
}
.upload-text.data-v-28698208 {
	font-size: 24rpx;
	color: #999;
}

/* 位置选择样式 */
.location-input.data-v-28698208 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 25rpx;
	background-color: #f5f5f5;
	border-radius: 12rpx;
}
.location-text.data-v-28698208 {
	font-size: 28rpx;
	color: #333;
}
.location-icon.data-v-28698208 {
	font-size: 32rpx;
}

/* 配置选择样式 */
.config-grid.data-v-28698208 {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}
.config-item.data-v-28698208 {
	display: flex;
	align-items: center;
	padding: 20rpx 25rpx;
	background-color: #f5f5f5;
	border-radius: 30rpx;
	border: 2rpx solid transparent;
}
.config-item.active.data-v-28698208 {
	background-color: #e8f4fd;
	border-color: #1890ff;
}
.config-icon.data-v-28698208 {
	font-size: 28rpx;
	margin-right: 10rpx;
}
.config-text.data-v-28698208 {
	font-size: 26rpx;
	color: #333;
}

/* 提交按钮样式 */
.submit-section.data-v-28698208 {
	padding: 40rpx;
}
.submit-btn.data-v-28698208 {
	width: 100%;
	padding: 30rpx;
	background-color: #3cc51f;
	color: #fff;
	border-radius: 12rpx;
	font-size: 32rpx;
	text-align: center;
}
.submit-btn[disabled].data-v-28698208 {
	background-color: #ccc;
}

