'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const { userId, reason = '违反平台规定' } = event;
	
	if (!userId) {
		return {
			success: false,
			message: '用户ID不能为空'
		};
	}
	
	try {
		// 检查用户是否存在
		const userResult = await db.collection('user').doc(userId).get();
		
		if (userResult.data.length === 0) {
			return {
				success: false,
				message: '用户不存在'
			};
		}
		
		const user = userResult.data[0];
		
		// 检查用户是否已经被封禁
		if (user.status === 'banned') {
			return {
				success: false,
				message: '用户已经被封禁'
			};
		}
		
		// 更新用户状态
		await db.collection('user').doc(userId).update({
			status: 'banned',
			ban_time: new Date(),
			ban_reason: reason
		});
		
		// 下架用户发布的所有房源
		await db.collection('house')
			.where({ owner_id: userId })
			.update({
				status: 'banned',
				ban_time: new Date()
			});
		
		// 记录封禁日志
		const banLog = {
			user_id: userId,
			user_nickname: user.nickname || '微信用户',
			action: 'ban',
			reason: reason,
			ban_time: new Date(),
			admin_ip: context.CLIENTIP
		};
		
		await db.collection('user_ban_logs').add(banLog);
		
		return {
			success: true,
			message: '用户封禁成功'
		};
		
	} catch (error) {
		console.error('封禁用户失败:', error);
		return {
			success: false,
			message: '封禁用户失败',
			error: error.message
		};
	}
};
