{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端8/pages/postHouse/postHouse.vue?41c9", "webpack:///D:/web/project/前端8/pages/postHouse/postHouse.vue?e633", "webpack:///D:/web/project/前端8/pages/postHouse/postHouse.vue?3a04", "webpack:///D:/web/project/前端8/pages/postHouse/postHouse.vue?5b0d", "uni-app:///pages/postHouse/postHouse.vue", "webpack:///D:/web/project/前端8/pages/postHouse/postHouse.vue?9df2", "webpack:///D:/web/project/前端8/pages/postHouse/postHouse.vue?5adf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formData", "title", "images", "price", "type", "location", "address", "latitude", "longitude", "config", "contact", "phone", "wechat", "desc", "typeList", "typeIndex", "configList", "submitting", "methods", "chooseImage", "uni", "count", "sizeType", "sourceType", "success", "uploadImages", "tempFilePaths", "filePath", "uniCloud", "cloudPath", "result", "console", "icon", "deleteImage", "onTypeChange", "chooseLocation", "fail", "toggleConfig", "getConfigIcon", "validateForm", "submitForm", "name", "res", "setTimeout", "url", "resetForm"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACqC;;;AAG7F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAooB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiGxpB;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;UACAC;QACA;QACAC;QACAC;UACAC;UACAC;QACA;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAL;kBAAAnB;gBAAA;gBAAA;gBAAA,uCAGAyB;gBAAA;gBAAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAAC;gBAAA;gBAAA,OACAC;kBACAD;kBACAE;gBACA;cAAA;gBAHAC;gBAIA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACAX;kBACAnB;kBACA+B;gBACA;cAAA;gBAAA;gBAEAZ;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAa;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACAf;QACAI;UACA;YACAlB;YACAC;YACAC;UACA;QACA;QACA4B;UACAL;UACAX;YACAnB;YACA+B;UACA;QACA;MACA;IACA;IAEA;IACAK;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAnB;UAAAnB;UAAA+B;QAAA;QACA;MACA;MAEA;QACAZ;UAAAnB;UAAA+B;QAAA;QACA;MACA;MAEA;QACAZ;UAAAnB;UAAA+B;QAAA;QACA;MACA;MAEA;QACAZ;UAAAnB;UAAA+B;QAAA;QACA;MACA;MAEA;QACAZ;UAAAnB;UAAA+B;QAAA;QACA;MACA;MAEA;QACAZ;UAAAnB;UAAA+B;QAAA;QACA;MACA;MAEA;IACA;IAEA;IACAQ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAAA;gBAAA,OAGAZ;kBACAa;kBACA1C;gBACA;cAAA;gBAHA2C;gBAKA;kBACAtB;oBACAnB;oBACA+B;kBACA;;kBAEA;kBACA;;kBAEA;kBACAW;oBACAvB;sBACAwB;oBACA;kBACA;gBACA;kBACAxB;oBACAnB;oBACA+B;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;gBACAX;kBACAnB;kBACA+B;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAa;MACA;QACA5C;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;UACAC;QACA;QACAC;QACAC;UACAC;UACAC;QACA;QACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/TA;AAAA;AAAA;AAAA;AAA27B,CAAgB,q5BAAG,EAAC,C;;;;;;;;;;;ACA/8B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/postHouse/postHouse.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/postHouse/postHouse.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./postHouse.vue?vue&type=template&id=28698208&scoped=true&\"\nvar renderjs\nimport script from \"./postHouse.vue?vue&type=script&lang=js&\"\nexport * from \"./postHouse.vue?vue&type=script&lang=js&\"\nimport style0 from \"./postHouse.vue?vue&type=style&index=0&id=28698208&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"28698208\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/postHouse/postHouse.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./postHouse.vue?vue&type=template&id=28698208&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.formData.images.length\n  var l0 = _vm.__map(_vm.configList, function (config, index) {\n    var $orig = _vm.__get_orig(config)\n    var g1 = _vm.formData.config.includes(config)\n    var m0 = _vm.getConfigIcon(config)\n    return {\n      $orig: $orig,\n      g1: g1,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./postHouse.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./postHouse.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"post-house\">\n\t\t<form @submit=\"submitForm\">\n\t\t\t<!-- 房源标题 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">房源标题</view>\n\t\t\t\t<input v-model=\"formData.title\" placeholder=\"请输入房源标题\" class=\"form-input\" maxlength=\"50\" />\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 房源图片 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">房源图片 (最多9张)</view>\n\t\t\t\t<view class=\"image-upload\">\n\t\t\t\t\t<view class=\"image-item\" v-for=\"(image, index) in formData.images\" :key=\"index\">\n\t\t\t\t\t\t<image :src=\"image\" mode=\"aspectFill\" class=\"uploaded-image\"></image>\n\t\t\t\t\t\t<text class=\"delete-btn\" @click=\"deleteImage(index)\">×</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"upload-btn\" v-if=\"formData.images.length < 9\" @click=\"chooseImage\">\n\t\t\t\t\t\t<text class=\"upload-icon\">+</text>\n\t\t\t\t\t\t<text class=\"upload-text\">添加图片</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 基本信息 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">基本信息</view>\n\t\t\t\t<view class=\"form-row\">\n\t\t\t\t\t<text class=\"form-label\">租金</text>\n\t\t\t\t\t<input v-model=\"formData.price\" type=\"number\" placeholder=\"请输入月租金\" class=\"form-input\" />\n\t\t\t\t\t<text class=\"form-unit\">元/月</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-row\">\n\t\t\t\t\t<text class=\"form-label\">户型</text>\n\t\t\t\t\t<picker :value=\"typeIndex\" :range=\"typeList\" @change=\"onTypeChange\" class=\"form-picker\">\n\t\t\t\t\t\t<view class=\"picker-text\">{{ typeList[typeIndex] || '请选择户型' }}</view>\n\t\t\t\t\t</picker>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 位置信息 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">位置信息</view>\n\t\t\t\t<view class=\"location-input\" @click=\"chooseLocation\">\n\t\t\t\t\t<text class=\"location-text\">{{ formData.location.address || '点击选择位置' }}</text>\n\t\t\t\t\t<text class=\"location-icon\">📍</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 房源配置 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">房源配置</view>\n\t\t\t\t<view class=\"config-grid\">\n\t\t\t\t\t<view class=\"config-item\" \n\t\t\t\t\t\t  v-for=\"(config, index) in configList\" \n\t\t\t\t\t\t  :key=\"index\"\n\t\t\t\t\t\t  :class=\"{ active: formData.config.includes(config) }\"\n\t\t\t\t\t\t  @click=\"toggleConfig(config)\">\n\t\t\t\t\t\t<text class=\"config-icon\">{{ getConfigIcon(config) }}</text>\n\t\t\t\t\t\t<text class=\"config-text\">{{ config }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 联系方式 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">联系方式</view>\n\t\t\t\t<view class=\"form-row\">\n\t\t\t\t\t<text class=\"form-label\">电话</text>\n\t\t\t\t\t<input v-model=\"formData.contact.phone\" placeholder=\"请输入联系电话\" class=\"form-input\" />\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-row\">\n\t\t\t\t\t<text class=\"form-label\">微信</text>\n\t\t\t\t\t<input v-model=\"formData.contact.wechat\" placeholder=\"请输入微信号\" class=\"form-input\" />\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 详细描述 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">详细描述</view>\n\t\t\t\t<textarea v-model=\"formData.desc\" \n\t\t\t\t\t\t  placeholder=\"请详细描述房源情况，如周边环境、交通便利性等\" \n\t\t\t\t\t\t  class=\"form-textarea\"\n\t\t\t\t\t\t  maxlength=\"500\"></textarea>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 提交按钮 -->\n\t\t\t<view class=\"submit-section\">\n\t\t\t\t<button class=\"submit-btn\" @click=\"submitForm\" :disabled=\"submitting\">\n\t\t\t\t\t{{ submitting ? '发布中...' : '发布房源' }}\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t</form>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tformData: {\n\t\t\t\ttitle: '',\n\t\t\t\timages: [],\n\t\t\t\tprice: '',\n\t\t\t\ttype: '',\n\t\t\t\tlocation: {\n\t\t\t\t\taddress: '',\n\t\t\t\t\tlatitude: '',\n\t\t\t\t\tlongitude: ''\n\t\t\t\t},\n\t\t\t\tconfig: [],\n\t\t\t\tcontact: {\n\t\t\t\t\tphone: '',\n\t\t\t\t\twechat: ''\n\t\t\t\t},\n\t\t\t\tdesc: ''\n\t\t\t},\n\t\t\ttypeList: ['一居室', '二居室', '三居室', '合租', '整租'],\n\t\t\ttypeIndex: -1,\n\t\t\tconfigList: ['空调', '洗衣机', '热水器', '冰箱', '电视', 'WiFi', '床', '衣柜', '桌椅', '阳台'],\n\t\t\tsubmitting: false\n\t\t}\n\t},\n\tmethods: {\n\t\t// 选择图片\n\t\tchooseImage() {\n\t\t\tconst remainCount = 9 - this.formData.images.length;\n\t\t\tuni.chooseImage({\n\t\t\t\tcount: remainCount,\n\t\t\t\tsizeType: ['compressed'],\n\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.uploadImages(res.tempFilePaths);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 上传图片\n\t\tasync uploadImages(tempFilePaths) {\n\t\t\tuni.showLoading({ title: '上传中...' });\n\t\t\t\n\t\t\ttry {\n\t\t\t\tfor (let filePath of tempFilePaths) {\n\t\t\t\t\tconst result = await uniCloud.uploadFile({\n\t\t\t\t\t\tfilePath: filePath,\n\t\t\t\t\t\tcloudPath: `house-images/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.jpg`\n\t\t\t\t\t});\n\t\t\t\t\tthis.formData.images.push(result.fileID);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('图片上传失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '图片上传失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 删除图片\n\t\tdeleteImage(index) {\n\t\t\tthis.formData.images.splice(index, 1);\n\t\t},\n\t\t\n\t\t// 选择户型\n\t\tonTypeChange(e) {\n\t\t\tthis.typeIndex = e.detail.value;\n\t\t\tthis.formData.type = this.typeList[this.typeIndex];\n\t\t},\n\t\t\n\t\t// 选择位置\n\t\tchooseLocation() {\n\t\t\tuni.chooseLocation({\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.formData.location = {\n\t\t\t\t\t\taddress: res.address,\n\t\t\t\t\t\tlatitude: res.latitude,\n\t\t\t\t\t\tlongitude: res.longitude\n\t\t\t\t\t};\n\t\t\t\t},\n\t\t\t\tfail: (error) => {\n\t\t\t\t\tconsole.error('选择位置失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请授权位置信息',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 切换配置\n\t\ttoggleConfig(config) {\n\t\t\tconst index = this.formData.config.indexOf(config);\n\t\t\tif (index > -1) {\n\t\t\t\tthis.formData.config.splice(index, 1);\n\t\t\t} else {\n\t\t\t\tthis.formData.config.push(config);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取配置图标\n\t\tgetConfigIcon(config) {\n\t\t\tconst iconMap = {\n\t\t\t\t'空调': '❄️',\n\t\t\t\t'洗衣机': '🧺',\n\t\t\t\t'热水器': '🚿',\n\t\t\t\t'冰箱': '🧊',\n\t\t\t\t'电视': '📺',\n\t\t\t\t'WiFi': '📶',\n\t\t\t\t'床': '🛏️',\n\t\t\t\t'衣柜': '👔',\n\t\t\t\t'桌椅': '🪑',\n\t\t\t\t'阳台': '🌅'\n\t\t\t};\n\t\t\treturn iconMap[config] || '✅';\n\t\t},\n\t\t\n\t\t// 表单验证\n\t\tvalidateForm() {\n\t\t\tif (!this.formData.title.trim()) {\n\t\t\t\tuni.showToast({ title: '请输入房源标题', icon: 'none' });\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\tif (this.formData.images.length === 0) {\n\t\t\t\tuni.showToast({ title: '请上传房源图片', icon: 'none' });\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.formData.price || this.formData.price <= 0) {\n\t\t\t\tuni.showToast({ title: '请输入正确的租金', icon: 'none' });\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.formData.type) {\n\t\t\t\tuni.showToast({ title: '请选择户型', icon: 'none' });\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.formData.location.address) {\n\t\t\t\tuni.showToast({ title: '请选择位置', icon: 'none' });\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.formData.contact.phone && !this.formData.contact.wechat) {\n\t\t\t\tuni.showToast({ title: '请至少填写一种联系方式', icon: 'none' });\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\treturn true;\n\t\t},\n\t\t\n\t\t// 提交表单\n\t\tasync submitForm() {\n\t\t\tif (!this.validateForm()) return;\n\t\t\t\n\t\t\tthis.submitting = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\tname: 'postHouse',\n\t\t\t\t\tdata: this.formData\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (res.result.success) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '发布成功，等待审核',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 清空表单\n\t\t\t\t\tthis.resetForm();\n\t\t\t\t\t\n\t\t\t\t\t// 跳转到我的发布页面\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/myPublish/myPublish'\n\t\t\t\t\t\t});\n\t\t\t\t\t}, 1500);\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.result.message || '发布失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('发布房源失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '发布失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.submitting = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 重置表单\n\t\tresetForm() {\n\t\t\tthis.formData = {\n\t\t\t\ttitle: '',\n\t\t\t\timages: [],\n\t\t\t\tprice: '',\n\t\t\t\ttype: '',\n\t\t\t\tlocation: {\n\t\t\t\t\taddress: '',\n\t\t\t\t\tlatitude: '',\n\t\t\t\t\tlongitude: ''\n\t\t\t\t},\n\t\t\t\tconfig: [],\n\t\t\t\tcontact: {\n\t\t\t\t\tphone: '',\n\t\t\t\t\twechat: ''\n\t\t\t\t},\n\t\t\t\tdesc: ''\n\t\t\t};\n\t\t\tthis.typeIndex = -1;\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.post-house {\n\tbackground-color: #f8f8f8;\n\tmin-height: 100vh;\n\tpadding-bottom: 40rpx;\n}\n\n/* 表单区域样式 */\n.form-section {\n\tbackground-color: #fff;\n\tmargin-bottom: 20rpx;\n\tpadding: 30rpx;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 30rpx;\n}\n\n.form-input {\n\twidth: 100%;\n\tpadding: 25rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.form-row {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.form-label {\n\twidth: 120rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.form-unit {\n\tmargin-left: 20rpx;\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.form-picker {\n\tflex: 1;\n\tpadding: 25rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 12rpx;\n}\n\n.picker-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.form-textarea {\n\twidth: 100%;\n\tmin-height: 200rpx;\n\tpadding: 25rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 12rpx;\n\tfont-size: 28rpx;\n}\n\n/* 图片上传样式 */\n.image-upload {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 20rpx;\n}\n\n.image-item {\n\tposition: relative;\n\twidth: 200rpx;\n\theight: 200rpx;\n}\n\n.uploaded-image {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 12rpx;\n}\n\n.delete-btn {\n\tposition: absolute;\n\ttop: -10rpx;\n\tright: -10rpx;\n\twidth: 40rpx;\n\theight: 40rpx;\n\tbackground-color: #ff4757;\n\tcolor: #fff;\n\tborder-radius: 50%;\n\ttext-align: center;\n\tline-height: 40rpx;\n\tfont-size: 24rpx;\n}\n\n.upload-btn {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 200rpx;\n\theight: 200rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 12rpx;\n\tborder: 2rpx dashed #ccc;\n}\n\n.upload-icon {\n\tfont-size: 48rpx;\n\tcolor: #999;\n\tmargin-bottom: 10rpx;\n}\n\n.upload-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n/* 位置选择样式 */\n.location-input {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 25rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 12rpx;\n}\n\n.location-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.location-icon {\n\tfont-size: 32rpx;\n}\n\n/* 配置选择样式 */\n.config-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 20rpx;\n}\n\n.config-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx 25rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 30rpx;\n\tborder: 2rpx solid transparent;\n}\n\n.config-item.active {\n\tbackground-color: #e8f4fd;\n\tborder-color: #1890ff;\n}\n\n.config-icon {\n\tfont-size: 28rpx;\n\tmargin-right: 10rpx;\n}\n\n.config-text {\n\tfont-size: 26rpx;\n\tcolor: #333;\n}\n\n/* 提交按钮样式 */\n.submit-section {\n\tpadding: 40rpx;\n}\n\n.submit-btn {\n\twidth: 100%;\n\tpadding: 30rpx;\n\tbackground-color: #3cc51f;\n\tcolor: #fff;\n\tborder-radius: 12rpx;\n\tfont-size: 32rpx;\n\ttext-align: center;\n}\n\n.submit-btn[disabled] {\n\tbackground-color: #ccc;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./postHouse.vue?vue&type=style&index=0&id=28698208&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./postHouse.vue?vue&type=style&index=0&id=28698208&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753953187024\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}