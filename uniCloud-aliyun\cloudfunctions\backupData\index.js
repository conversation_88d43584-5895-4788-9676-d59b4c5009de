'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	try {
		const backupTime = new Date();
		const backupId = `backup_${backupTime.getTime()}`;
		
		// 统计各个集合的数据量
		const collections = ['house', 'user', 'admin', 'user_collect', 'visit_logs', 'audit_logs'];
		const backupStats = {};
		
		for (const collectionName of collections) {
			try {
				const result = await db.collection(collectionName).count();
				backupStats[collectionName] = result.total;
			} catch (error) {
				console.log(`集合 ${collectionName} 不存在或查询失败:`, error);
				backupStats[collectionName] = 0;
			}
		}
		
		// 记录备份信息
		const backupRecord = {
			backup_id: backupId,
			backup_time: backupTime,
			backup_stats: backupStats,
			backup_type: 'manual', // 手动备份
			admin_ip: context.CLIENTIP,
			status: 'completed'
		};
		
		await db.collection('backup_logs').add(backupRecord);
		
		// 清理旧的备份记录（保留最近10次）
		try {
			const oldBackupsResult = await db.collection('backup_logs')
				.orderBy('backup_time', 'desc')
				.skip(10)
				.get();
			
			for (const oldBackup of oldBackupsResult.data) {
				await db.collection('backup_logs').doc(oldBackup._id).remove();
			}
		} catch (error) {
			console.log('清理旧备份记录失败:', error);
		}
		
		return {
			success: true,
			message: '数据备份成功',
			data: {
				backupId: backupId,
				backupTime: backupTime,
				stats: backupStats
			}
		};
		
	} catch (error) {
		console.error('数据备份失败:', error);
		
		// 记录备份失败日志
		try {
			await db.collection('backup_logs').add({
				backup_id: `failed_${Date.now()}`,
				backup_time: new Date(),
				backup_type: 'manual',
				admin_ip: context.CLIENTIP,
				status: 'failed',
				error_message: error.message
			});
		} catch (logError) {
			console.error('记录备份失败日志失败:', logError);
		}
		
		return {
			success: false,
			message: '数据备份失败',
			error: error.message
		};
	}
};
