'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	try {
		// 获取系统设置
		const settingsResult = await db.collection('system_settings')
			.orderBy('updated_at', 'desc')
			.limit(1)
			.get();
		
		let settings = {};
		
		if (settingsResult.data.length > 0) {
			settings = settingsResult.data[0];
			// 移除不需要返回的字段
			delete settings._id;
			delete settings.created_at;
			delete settings.updated_at;
		} else {
			// 如果没有设置记录，返回默认设置
			settings = {
				homeNotice: '欢迎使用房地产租赁平台！',
				noticeEnabled: true,
				autoAudit: false,
				allowRegister: true,
				dailyPublishLimit: 0,
				recommendAreas: '市中心,大学城,高新区',
				hotTypes: '一居室,二居室,三居室'
			};
			
			// 创建默认设置记录
			await db.collection('system_settings').add({
				...settings,
				created_at: new Date(),
				updated_at: new Date()
			});
		}
		
		return {
			success: true,
			data: settings
		};
		
	} catch (error) {
		console.error('获取系统设置失败:', error);
		return {
			success: false,
			message: '获取系统设置失败',
			error: error.message
		};
	}
};
