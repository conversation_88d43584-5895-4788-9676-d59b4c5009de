'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	const { houseId, status, reason = '' } = event;
	
	if (!houseId || !status) {
		return {
			success: false,
			message: '参数不完整'
		};
	}
	
	if (!['approved', 'rejected'].includes(status)) {
		return {
			success: false,
			message: '无效的审核状态'
		};
	}
	
	try {
		// 检查房源是否存在
		const houseResult = await db.collection('house').doc(houseId).get();
		
		if (houseResult.data.length === 0) {
			return {
				success: false,
				message: '房源不存在'
			};
		}
		
		const house = houseResult.data[0];
		
		// 更新房源状态
		const updateData = {
			status: status,
			audit_time: new Date(),
			audit_reason: reason
		};
		
		await db.collection('house').doc(houseId).update(updateData);
		
		// 记录审核日志
		const auditLog = {
			house_id: houseId,
			house_title: house.title,
			old_status: house.status,
			new_status: status,
			reason: reason,
			audit_time: new Date(),
			admin_ip: context.CLIENTIP
		};
		
		await db.collection('audit_logs').add(auditLog);
		
		// 如果是驳回，可以考虑发送通知给房源发布者
		if (status === 'rejected') {
			// 这里可以添加通知逻辑
			console.log(`房源 ${house.title} 被驳回，原因：${reason}`);
		}
		
		return {
			success: true,
			message: status === 'approved' ? '审核通过' : '已驳回',
			data: {
				houseId: houseId,
				status: status,
				reason: reason
			}
		};
		
	} catch (error) {
		console.error('房源审核失败:', error);
		return {
			success: false,
			message: '审核失败',
			error: error.message
		};
	}
};
