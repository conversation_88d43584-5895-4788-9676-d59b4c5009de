{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端8/pages/home/<USER>", "webpack:///D:/web/project/前端8/pages/home/<USER>", "webpack:///D:/web/project/前端8/pages/home/<USER>", "webpack:///D:/web/project/前端8/pages/home/<USER>", "uni-app:///pages/home/<USER>", "webpack:///D:/web/project/前端8/pages/home/<USER>", "webpack:///D:/web/project/前端8/pages/home/<USER>"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "bannerList", "image", "recommendHouses", "statistics", "todayPublish", "todayVisit", "totalHouse", "onLoad", "onPullDownRefresh", "setTimeout", "uni", "methods", "loadRecommendHouses", "uniCloud", "name", "pageSize", "recommend", "res", "console", "loadStatistics", "goToSearch", "url", "goToMap", "title", "icon", "goToPost", "goToCollect", "goToFilter", "goToHouseList", "goToDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACqC;;;AAGxF;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAA+nB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6EnpB;EACAC;IACA;MACAC,aACA;QAAAC;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAf;oBACAgB;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAOA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAN;kBACAC;kBACAf;gBACA;cAAA;gBAHAkB;gBAIA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MACAV;QACAW;MACA;IACA;IAEA;IACAC;MACAZ;QACAa;QACAC;MACA;IACA;IAEA;IACAC;MACAf;QACAW;MACA;IACA;IAEA;IACAK;MACAhB;QACAW;MACA;IACA;IAEA;IACAM;MACAjB;QACAW;MACA;IACA;IAEA;IACAO;MACAlB;QACAW;MACA;IACA;IAEA;IACAQ;MACAnB;QACAW;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5LA;AAAA;AAAA;AAAA;AAAs7B,CAAgB,g5BAAG,EAAC,C;;;;;;;;;;;ACA18B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/home/<USER>", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/home/<USER>'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./home.vue?vue&type=template&id=92bb8f34&scoped=true&\"\nvar renderjs\nimport script from \"./home.vue?vue&type=script&lang=js&\"\nexport * from \"./home.vue?vue&type=script&lang=js&\"\nimport style0 from \"./home.vue?vue&type=style&index=0&id=92bb8f34&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"92bb8f34\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/home/<USER>\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=template&id=92bb8f34&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.__map(_vm.recommendHouses, function (house, index) {\n    var $orig = _vm.__get_orig(house)\n    var l0 = house.config.slice(0, 3)\n    return {\n      $orig: $orig,\n      l0: l0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"home\">\n\t\t<!-- Banner轮播图 -->\n\t\t<swiper class=\"banner\" indicator-dots=\"true\" autoplay=\"true\" interval=\"3000\" duration=\"500\">\n\t\t\t<swiper-item v-for=\"(item, index) in bannerList\" :key=\"index\">\n\t\t\t\t<image :src=\"item.image\" mode=\"aspectFill\" class=\"banner-image\"></image>\n\t\t\t</swiper-item>\n\t\t</swiper>\n\t\t\n\t\t<!-- 搜索栏 -->\n\t\t<view class=\"search-bar\">\n\t\t\t<view class=\"search-input\" @click=\"goToSearch\">\n\t\t\t\t<text class=\"search-placeholder\">搜索房源、位置、价格...</text>\n\t\t\t\t<text class=\"search-icon\">🔍</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 快捷入口 -->\n\t\t<view class=\"quick-nav\">\n\t\t\t<view class=\"nav-item\" @click=\"goToMap\">\n\t\t\t\t<text class=\"nav-icon\">📍</text>\n\t\t\t\t<text class=\"nav-text\">地图找房</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-item\" @click=\"goToPost\">\n\t\t\t\t<text class=\"nav-icon\">📤</text>\n\t\t\t\t<text class=\"nav-text\">发布信息</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-item\" @click=\"goToCollect\">\n\t\t\t\t<text class=\"nav-icon\">❤️</text>\n\t\t\t\t<text class=\"nav-text\">我的收藏</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-item\" @click=\"goToFilter\">\n\t\t\t\t<text class=\"nav-icon\">🏷️</text>\n\t\t\t\t<text class=\"nav-text\">筛选房源</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 推荐房源 -->\n\t\t<view class=\"recommend-section\">\n\t\t\t<view class=\"section-title\">\n\t\t\t\t<text class=\"title-text\">推荐房源</text>\n\t\t\t\t<text class=\"more-text\" @click=\"goToHouseList\">更多 ></text>\n\t\t\t</view>\n\t\t\t<view class=\"house-list\">\n\t\t\t\t<view class=\"house-item\" v-for=\"(house, index) in recommendHouses\" :key=\"index\" @click=\"goToDetail(house._id)\">\n\t\t\t\t\t<image :src=\"house.images[0]\" mode=\"aspectFill\" class=\"house-image\"></image>\n\t\t\t\t\t<view class=\"house-info\">\n\t\t\t\t\t\t<text class=\"house-title\">{{ house.title }}</text>\n\t\t\t\t\t\t<text class=\"house-price\">¥{{ house.price }}/月</text>\n\t\t\t\t\t\t<text class=\"house-location\">{{ house.location.address }}</text>\n\t\t\t\t\t\t<view class=\"house-tags\">\n\t\t\t\t\t\t\t<text class=\"tag\" v-for=\"(tag, tagIndex) in house.config.slice(0, 3)\" :key=\"tagIndex\">{{ tag }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 统计信息 -->\n\t\t<view class=\"statistics\">\n\t\t\t<view class=\"stat-item\">\n\t\t\t\t<text class=\"stat-number\">{{ statistics.todayPublish }}</text>\n\t\t\t\t<text class=\"stat-label\">今日发布</text>\n\t\t\t</view>\n\t\t\t<view class=\"stat-item\">\n\t\t\t\t<text class=\"stat-number\">{{ statistics.todayVisit }}</text>\n\t\t\t\t<text class=\"stat-label\">今日访问</text>\n\t\t\t</view>\n\t\t\t<view class=\"stat-item\">\n\t\t\t\t<text class=\"stat-number\">{{ statistics.totalHouse }}</text>\n\t\t\t\t<text class=\"stat-label\">房源总数</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tbannerList: [\n\t\t\t\t{ image: '/static/banner1.jpg' },\n\t\t\t\t{ image: '/static/banner2.jpg' },\n\t\t\t\t{ image: '/static/banner3.jpg' }\n\t\t\t],\n\t\t\trecommendHouses: [],\n\t\t\tstatistics: {\n\t\t\t\ttodayPublish: 0,\n\t\t\t\ttodayVisit: 0,\n\t\t\t\ttotalHouse: 0\n\t\t\t}\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.loadRecommendHouses();\n\t\tthis.loadStatistics();\n\t},\n\tonPullDownRefresh() {\n\t\tthis.loadRecommendHouses();\n\t\tthis.loadStatistics();\n\t\tsetTimeout(() => {\n\t\t\tuni.stopPullDownRefresh();\n\t\t}, 1000);\n\t},\n\tmethods: {\n\t\t// 加载推荐房源\n\t\tasync loadRecommendHouses() {\n\t\t\ttry {\n\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\tname: 'getHouseList',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tpageSize: 6,\n\t\t\t\t\t\trecommend: true\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tif (res.result.success) {\n\t\t\t\t\tthis.recommendHouses = res.result.data;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载推荐房源失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载统计数据\n\t\tasync loadStatistics() {\n\t\t\ttry {\n\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\tname: 'getStatistics',\n\t\t\t\t\tdata: {}\n\t\t\t\t});\n\t\t\t\tif (res.result.success) {\n\t\t\t\t\tthis.statistics = res.result.data;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载统计数据失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 跳转到搜索页面\n\t\tgoToSearch() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/houseList/houseList?search=true'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到地图找房\n\t\tgoToMap() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '地图找房功能开发中',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到发布页面\n\t\tgoToPost() {\n\t\t\tuni.switchTab({\n\t\t\t\turl: '/pages/postHouse/postHouse'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到收藏页面\n\t\tgoToCollect() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/myCollect/myCollect'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到筛选页面\n\t\tgoToFilter() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/houseList/houseList?filter=true'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到房源列表\n\t\tgoToHouseList() {\n\t\t\tuni.switchTab({\n\t\t\t\turl: '/pages/houseList/houseList'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 跳转到房源详情\n\t\tgoToDetail(houseId) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/houseDetail/houseDetail?id=${houseId}`\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.home {\n\tbackground-color: #f8f8f8;\n\tmin-height: 100vh;\n}\n\n/* Banner样式 */\n.banner {\n\twidth: 100%;\n\theight: 400rpx;\n}\n\n.banner-image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n/* 搜索栏样式 */\n.search-bar {\n\tpadding: 20rpx;\n\tbackground-color: #fff;\n}\n\n.search-input {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx 30rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 50rpx;\n}\n\n.search-placeholder {\n\tcolor: #999;\n\tfont-size: 28rpx;\n}\n\n.search-icon {\n\tfont-size: 32rpx;\n}\n\n/* 快捷导航样式 */\n.quick-nav {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\tpadding: 40rpx 20rpx;\n\tbackground-color: #fff;\n\tmargin-top: 20rpx;\n}\n\n.nav-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.nav-icon {\n\tfont-size: 48rpx;\n\tmargin-bottom: 10rpx;\n}\n\n.nav-text {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n/* 推荐房源样式 */\n.recommend-section {\n\tmargin-top: 20rpx;\n\tbackground-color: #fff;\n\tpadding: 30rpx 20rpx;\n}\n\n.section-title {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 30rpx;\n}\n\n.title-text {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.more-text {\n\tfont-size: 28rpx;\n\tcolor: #3cc51f;\n}\n\n.house-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.house-item {\n\tdisplay: flex;\n\tpadding: 20rpx;\n\tbackground-color: #f9f9f9;\n\tborder-radius: 16rpx;\n}\n\n.house-image {\n\twidth: 200rpx;\n\theight: 150rpx;\n\tborder-radius: 12rpx;\n\tmargin-right: 20rpx;\n}\n\n.house-info {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: space-between;\n}\n\n.house-title {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n}\n\n.house-price {\n\tfont-size: 32rpx;\n\tcolor: #ff6b35;\n\tfont-weight: bold;\n\tmargin-bottom: 10rpx;\n}\n\n.house-location {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tmargin-bottom: 10rpx;\n}\n\n.house-tags {\n\tdisplay: flex;\n\tgap: 10rpx;\n}\n\n.tag {\n\tpadding: 4rpx 12rpx;\n\tbackground-color: #e8f4fd;\n\tcolor: #1890ff;\n\tfont-size: 22rpx;\n\tborder-radius: 8rpx;\n}\n\n/* 统计信息样式 */\n.statistics {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\tpadding: 40rpx 20rpx;\n\tbackground-color: #fff;\n\tmargin-top: 20rpx;\n}\n\n.stat-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.stat-number {\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\tcolor: #3cc51f;\n\tmargin-bottom: 10rpx;\n}\n\n.stat-label {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=style&index=0&id=92bb8f34&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=style&index=0&id=92bb8f34&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753953187022\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}