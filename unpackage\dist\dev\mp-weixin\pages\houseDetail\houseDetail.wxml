<block wx:if="{{houseInfo}}"><view class="house-detail data-v-6992ea2a"><swiper class="image-swiper data-v-6992ea2a" indicator-dots="true" autoplay="false"><block wx:for="{{houseInfo.images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><swiper-item class="data-v-6992ea2a"><image class="detail-image data-v-6992ea2a" src="{{image}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',[index]]]]]}}" bindtap="__e"></image></swiper-item></block></swiper><view class="basic-info data-v-6992ea2a"><view class="price-section data-v-6992ea2a"><text class="price data-v-6992ea2a">{{"¥"+houseInfo.price}}</text><text class="price-unit data-v-6992ea2a">/月</text><view data-event-opts="{{[['tap',[['toggleCollect',['$event']]]]]}}" class="collect-btn data-v-6992ea2a" bindtap="__e"><text class="collect-icon data-v-6992ea2a">{{houseInfo.isCollected?'❤️':'🤍'}}</text><text class="collect-text data-v-6992ea2a">{{houseInfo.isCollected?'已收藏':'收藏'}}</text></view></view><view class="title data-v-6992ea2a">{{houseInfo.title}}</view><view class="location data-v-6992ea2a"><text class="location-icon data-v-6992ea2a">📍</text><text class="location-text data-v-6992ea2a">{{houseInfo.location.address}}</text></view><view class="house-tags data-v-6992ea2a"><text class="tag type-tag data-v-6992ea2a">{{houseInfo.type}}</text><text class="{{['tag','status-tag','data-v-6992ea2a',houseInfo.status]}}">{{$root.m0}}</text></view></view><view class="config-section data-v-6992ea2a"><view class="section-title data-v-6992ea2a">房源配置</view><view class="config-grid data-v-6992ea2a"><block wx:for="{{$root.l0}}" wx:for-item="config" wx:for-index="index" wx:key="index"><view class="config-item data-v-6992ea2a"><text class="config-icon data-v-6992ea2a">{{config.m1}}</text><text class="config-text data-v-6992ea2a">{{config.$orig}}</text></view></block></view></view><view class="description-section data-v-6992ea2a"><view class="section-title data-v-6992ea2a">房源描述</view><text class="description-text data-v-6992ea2a">{{houseInfo.desc}}</text></view><view class="contact-section data-v-6992ea2a"><view class="section-title data-v-6992ea2a">联系房东</view><view class="contact-info data-v-6992ea2a"><block wx:if="{{houseInfo.contact.phone}}"><view class="contact-item data-v-6992ea2a"><text class="contact-label data-v-6992ea2a">电话：</text><text class="contact-value data-v-6992ea2a">{{houseInfo.contact.phone}}</text><text data-event-opts="{{[['tap',[['makeCall',['$event']]]]]}}" class="contact-btn data-v-6992ea2a" bindtap="__e">拨打</text></view></block><block wx:if="{{houseInfo.contact.wechat}}"><view class="contact-item data-v-6992ea2a"><text class="contact-label data-v-6992ea2a">微信：</text><text class="contact-value data-v-6992ea2a">{{houseInfo.contact.wechat}}</text><text data-event-opts="{{[['tap',[['copyWechat',['$event']]]]]}}" class="contact-btn data-v-6992ea2a" bindtap="__e">复制</text></view></block></view></view><view class="report-section data-v-6992ea2a"><text data-event-opts="{{[['tap',[['reportHouse',['$event']]]]]}}" class="report-btn data-v-6992ea2a" bindtap="__e">举报房源</text></view><view class="bottom-actions data-v-6992ea2a"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="action-btn secondary data-v-6992ea2a" bindtap="__e"><text class="data-v-6992ea2a">返回</text></view><view data-event-opts="{{[['tap',[['contactOwner',['$event']]]]]}}" class="action-btn primary data-v-6992ea2a" bindtap="__e"><text class="data-v-6992ea2a">立即联系</text></view></view></view></block><block wx:else><view class="loading data-v-6992ea2a"><text class="data-v-6992ea2a">加载中...</text></view></block>