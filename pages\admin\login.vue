<template>
	<view class="admin-login">
		<view class="login-container">
			<view class="login-header">
				<image src="/static/logo.png" class="logo"></image>
				<text class="title">管理后台</text>
				<text class="subtitle">房地产租赁管理系统</text>
			</view>
			
			<view class="login-form">
				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">👤</text>
						<input v-model="loginForm.username" 
							   placeholder="请输入管理员账号" 
							   class="form-input" 
							   type="text" />
					</view>
				</view>
				
				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔒</text>
						<input v-model="loginForm.password" 
							   placeholder="请输入密码" 
							   class="form-input" 
							   type="password" />
					</view>
				</view>
				
				<view class="form-item">
					<button class="login-btn" 
							@click="handleLogin" 
							:disabled="loginLoading">
						{{ loginLoading ? '登录中...' : '登录' }}
					</button>
				</view>
				
				<view class="login-tips">
					<text class="tip-text">默认账号：admin，密码：123456</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			loginForm: {
				username: '',
				password: ''
			},
			loginLoading: false
		}
	},
	onLoad() {
		// 检查是否已经登录
		this.checkLoginStatus();
	},
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			const adminToken = uni.getStorageSync('adminToken');
			if (adminToken) {
				// 已登录，跳转到管理后台首页
				uni.redirectTo({
					url: '/pages/admin/dashboard'
				});
			}
		},
		
		// 表单验证
		validateForm() {
			if (!this.loginForm.username.trim()) {
				uni.showToast({
					title: '请输入管理员账号',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.loginForm.password.trim()) {
				uni.showToast({
					title: '请输入密码',
					icon: 'none'
				});
				return false;
			}
			
			return true;
		},
		
		// 处理登录
		async handleLogin() {
			if (!this.validateForm()) return;
			
			this.loginLoading = true;
			
			try {
				const res = await uniCloud.callFunction({
					name: 'adminLogin',
					data: {
						username: this.loginForm.username,
						password: this.loginForm.password
					}
				});
				
				if (res.result.success) {
					// 保存登录信息
					uni.setStorageSync('adminToken', res.result.token);
					uni.setStorageSync('adminInfo', res.result.adminInfo);
					
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					});
					
					// 跳转到管理后台首页
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages/admin/dashboard'
						});
					}, 1500);
				} else {
					uni.showToast({
						title: res.result.message || '登录失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('登录失败:', error);
				uni.showToast({
					title: '登录失败，请重试',
					icon: 'none'
				});
			} finally {
				this.loginLoading = false;
			}
		}
	}
}
</script>

<style scoped>
.admin-login {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.login-container {
	width: 100%;
	max-width: 600rpx;
	background-color: #fff;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
}

.login-header {
	text-align: center;
	margin-bottom: 60rpx;
}

.logo {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	margin-bottom: 30rpx;
}

.title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
}

.subtitle {
	display: block;
	font-size: 28rpx;
	color: #666;
}

.login-form {
	width: 100%;
}

.form-item {
	margin-bottom: 40rpx;
}

.input-wrapper {
	display: flex;
	align-items: center;
	background-color: #f8f9fa;
	border-radius: 12rpx;
	padding: 0 30rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.input-wrapper:focus-within {
	border-color: #667eea;
	background-color: #fff;
	box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.input-icon {
	font-size: 36rpx;
	margin-right: 20rpx;
	color: #999;
}

.form-input {
	flex: 1;
	height: 100rpx;
	font-size: 32rpx;
	color: #333;
	background-color: transparent;
	border: none;
}

.login-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	border: none;
	border-radius: 12rpx;
	font-size: 32rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.login-btn:not([disabled]):active {
	transform: translateY(2rpx);
}

.login-btn[disabled] {
	opacity: 0.6;
	transform: none;
}

.login-tips {
	text-align: center;
	margin-top: 40rpx;
}

.tip-text {
	font-size: 24rpx;
	color: #999;
}
</style>
