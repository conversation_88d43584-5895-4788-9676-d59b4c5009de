'use strict';

const db = uniCloud.database();
const collection = db.collection('house');

exports.main = async (event, context) => {
	const {
		page = 1,
		pageSize = 10,
		keyword = '',
		area = '',
		price = '',
		type = '',
		recommend = false,
		status = 'approved'
	} = event;
	
	try {
		// 构建查询条件
		let whereCondition = {
			status: status
		};
		
		// 关键词搜索
		if (keyword) {
			whereCondition.$or = [
				{ title: new RegExp(keyword, 'i') },
				{ desc: new RegExp(keyword, 'i') },
				{ 'location.address': new RegExp(keyword, 'i') }
			];
		}
		
		// 区域筛选
		if (area) {
			whereCondition['location.address'] = new RegExp(area, 'i');
		}
		
		// 价格筛选
		if (price) {
			const [minPrice, maxPrice] = price.split('-').map(Number);
			whereCondition.price = {
				$gte: minPrice,
				$lte: maxPrice
			};
		}
		
		// 户型筛选
		if (type) {
			whereCondition.type = type;
		}
		
		// 推荐房源（按创建时间倒序，取最新的）
		let query = collection.where(whereCondition);
		
		if (recommend) {
			// 推荐房源逻辑：最新发布的房源
			query = query.orderBy('created_at', 'desc');
		} else {
			// 普通列表按创建时间倒序
			query = query.orderBy('created_at', 'desc');
		}
		
		// 分页
		const skip = (page - 1) * pageSize;
		query = query.skip(skip).limit(pageSize);
		
		// 执行查询
		const result = await query.get();
		
		// 处理返回数据，添加收藏状态等
		const houseList = result.data.map(house => {
			return {
				...house,
				isCollected: false, // 这里需要根据用户ID查询收藏状态
				images: house.images || ['/static/default-house.jpg'] // 默认图片
			};
		});
		
		return {
			success: true,
			data: houseList,
			total: result.data.length,
			page: page,
			pageSize: pageSize
		};
		
	} catch (error) {
		console.error('获取房源列表失败:', error);
		return {
			success: false,
			message: '获取房源列表失败',
			error: error.message
		};
	}
};
